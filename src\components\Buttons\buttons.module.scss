.button {
	border: none;
	background-color: transparent;

	font-family: inherit;
	font-weight: 700;
	display: flex;
	height: 35px;
	align-items: center;
	&:hover .text {
		background-color: rgba($color: #000000, $alpha: 0.15);
	}
	&:hover .icon {
		background-color: rgba($color: #000000, $alpha: 0.3);
	}
}
.small-button {
	border: none;
	background-color: transparent;
	color: #fff;
	font-family: inherit;
	font-size: 14px;
	border: 1px solid;
	border-radius: 5px;
	height: 30px;
	padding: 5px;
	display: flex;
	align-items: center;
	justify-content: center;
}
.icon {
	height: 100%;
	background-color: rgba($color: #000000, $alpha: 0.4);
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 5px 10px;
	transition: all ease-in-out 0.3s;
}
.text {
	transition: all ease-in-out 0.3s;
	height: 100%;
	flex: 1;
	padding: 5px 15px;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 5px;
}
