import { useEffect, useState } from "react";
import {
	<PERSON><PERSON>,
	<PERSON>,
	CardHeader,
	CardBody,
	Table,
	TableHeader,
	TableColumn,
	TableBody,
	TableRow,
	TableCell,
	Input,
	Tooltip,
	Autocomplete,
	AutocompleteItem,
	RadioGroup,
	Radio,
	Checkbox,
	Select,
	SelectItem,
	NumberInput,
} from "@heroui/react";
import { Trash, X, Save } from "@mynaui/icons-react";
import { useMutation, useQuery } from "react-query";
import {
	addReceive,
	getAllClauses,
	getAllDuesLists,
	getReceiveById,
} from "../../../api/serverApi";
import { toast } from "react-toastify";
import Row from "../../../UI/row/Row";
import TopBar from "../../../components/TopBar/TopBar";
import useNavigateWithQuery from "@/hooks/useNavigateWithQuery";
import { useLocation } from "react-router-dom";
const ReceiveEditFormPage = () => {
	//hooks
	const navigate = useNavigateWithQuery();
	const info = useLocation();
	//states
	const [from, setFrom] = useState("");
	const [added, setAdded] = useState([]);
	const [isDues, setIsDues] = useState(false);
	const [duesLists, setDuesLists] = useState([]);
	const [addedCount, setAddedCount] = useState("");
	const [clause, setClause] = useState("");
	const [type, setType] = useState("نقدي");
	const [checkNumber, setCheckNumber] = useState("");
	const [date, setDate] = useState(new Date().toISOString().split("T")[0]);
	const [amount, setAmount] = useState(0);
	const [tax, setTax] = useState(0);
	const [netAmount, setNetAmount] = useState(0);

	//queries
	const { data: clauses } = useQuery({
		queryKey: ["clauses"],
		queryFn: getAllClauses,
		select: (res) => res.data.clauses,
	});
	const { data: receiveData } = useQuery({
		queryKey: ["receiveData", info.state?.id],
		queryFn: getReceiveById,
		select: (res) => {
			return res.data.receive;
		},
		onSuccess: (data) => {
			const added = data.duesLists.map((el, i) => {
				return {
					...el,
					dueList_id: el.id,
					id: i + 1,
					// amount: 0,
					// tax: 0,
					// net_amount: 0,
					// title: "",
				};
			});
			console.log(`added`, added);
			setFrom(data.money_from);
			setIsDues(data.duesLists.length > 0 ? true : false);
			setClause(`${data.clause_id}`);
			setType(data.check_number ? "شيك" : "نقدي");
			setCheckNumber(data.check_number);
			setDate(data.date);
			setAmount(data.amount);
			setTax(data.tax);
			setNetAmount(data.net_amount);
			// setDuesLists(data.duesLists);
			setAdded(added);
			setAddedCount(added.length);
		},
	});
	useQuery({
		queryKey: ["duesLists", 0, 0, "تمت المراجعة", clause],
		queryFn: getAllDuesLists,
		select: (res) => {
			return res.data.duesLists.map((el) => el);
		},
		onSuccess: (data) => {
			setDuesLists(data);
		},
		enabled: isDues && !!clause,
	});

	const addMutation = useMutation({
		mutationFn: addReceive,
		onSuccess: () => {
			toast.success("تم قبض المبلغ بنجاح", {
				position: toast.POSITION.TOP_CENTER,
			});
			navigate("./..");
		},
		onError: (err) => {
			toast.error(err.response.data.message, {
				position: toast.POSITION.TOP_CENTER,
			});
		},
	});
	//functions
	useEffect(() => {
		let total = 0;
		let tax = 0;
		let net_amount = 0;
		const updatedDuesLists = duesLists.map((duesList) => {
			const match = added.find(
				(addedDuesList) => +addedDuesList.dueList_id === +duesList.id
			);
			if (match) return { ...duesList, disabled: true };
			return { ...duesList, disabled: false };
		});
		added.forEach((el) => {
			total = total + +el.amount;
			tax = tax + +el.tax;
			net_amount = net_amount + +el.net_amount;
		});
		setAmount(total);
		setTax(tax);
		setNetAmount(net_amount);
		setDuesLists(updatedDuesLists);
	}, [added]);
	const submitHandler = (e) => {
		e.preventDefault();
		console.log(`foo`, {
			from,
			date,
			type,
			added,
			amount,
			tax,
			net_amount: netAmount,
			checkNumber: checkNumber ? checkNumber : null,
		});
		addMutation.mutate({
			from,
			date,
			type,
			added,
			amount,
			tax,
			clause,
			net_amount: netAmount,
			checkNumber: checkNumber ? checkNumber : null,
		});
	};
	const addHandler = () => {
		setAdded((prev) => [
			...prev,
			{
				id: addedCount + 1,
				dueList_id: "",
				amount: 0,
				tax: 0,
				net_amount: 0,
				title: "",
			},
		]);
		setAddedCount((prev) => prev + 1);
	};
	const dueListIdChangeHandler = (dueList_id, fieldId) => {
		const updated = added.map((el) => {
			if (el.id === fieldId) {
				const match = duesLists.find((el) => el.id === +dueList_id);
				return {
					...el,
					dueList_id,
					amount: match.amount,
					tax: match.tax,
					title: match.title,
					net_amount: match.net_amount,
				};
			}
			return el;
		});

		setAdded(updated);
	};
	// effects

	return (
		<div className="w-full h-full overflow-auto">
			<form onSubmit={submitHandler}>
				<TopBar
					right={
						<div className="flex gap-3">
							<Button
								variant="flat"
								color="warning"
								onPress={() => navigate("./..")}
								startContent={<X />}
							>
								الغاء
							</Button>
							<Button
								color="primary"
								type="submit"
								isDisabled={addMutation.isLoading}
								startContent={<Save />}
							>
								حفظ
							</Button>
						</div>
					}
				/>
				<div className="w-full p-5 pb-16">
					<Card>
						<CardHeader className="bg-primary text-default-50 font-bold text-medium">
							بيانات القبض
						</CardHeader>
						<CardBody>
							<Row flex={[2, 2, 1, 2]}>
								<Input
									value={from}
									onChange={(e) => setFrom(e.target.value)}
									label="جهة التسليم"
									size="sm"
									isRequired
								/>
								<Select
									selectedKeys={clause}
									label="البند"
									size="sm"
									onChange={(e) => {
										setAdded([]);
										setClause(e.target.value);
									}}
								>
									{clauses &&
										clauses.map((el) => (
											<SelectItem className="text-right" key={el.id}>
												{el.name}
											</SelectItem>
										))}
								</Select>
								<RadioGroup
									orientation="horizontal"
									value={type}
									isRequired
									label="نوع القبض"
									className="text-right"
									onChange={(e) => {
										setType(e.target.value);
										setCheckNumber("");
									}}
								>
									<Radio value="نقدي">نقدي</Radio>
									<Radio value="شيك">شيك</Radio>
								</RadioGroup>
								{type === "شيك" && (
									<Input
										size="sm"
										isRequired
										label="رقم الشيك"
										value={checkNumber}
										onChange={(e) => setCheckNumber(e.target.value)}
									/>
								)}
							</Row>
							<Row flex={[1.3, 1, 2, 4]}>
								<Input
									type="date"
									label="التاريخ"
									value={date}
									isRequired
									size="sm"
									onChange={(e) => setDate(e.target.value)}
								/>

								<div className="flex h-12 ">
									<Checkbox
										isSelected={isDues}
										onValueChange={setIsDues}
										onChange={() => {
											setAdded([]);
										}}
									>
										مستحقات
									</Checkbox>
								</div>
								{!isDues ? (
									<NumberInput
										size="sm"
										isRequired
										label="المبلغ"
										type="number"
										hideStepper
										min={0}
										isWheelDisabled
										onFocus={(e) => e.target.select()}
										value={netAmount}
										endContent={
											<div className="pointer-events-none flex items-center">
												<span className="text-default-400 text-small">ر.ي</span>
											</div>
										}
										onValueChange={(value) => {
											setAmount(value);
											setNetAmount(value);
											setTax(0);
										}}
									/>
								) : (
									<></>
								)}
								<></>
							</Row>
						</CardBody>
					</Card>
					{duesLists && isDues && (
						<Card>
							<CardHeader className="bg-primary text-default-50 font-bold text-medium">
								الكشوفات
							</CardHeader>
							<CardBody>
								<Table aria-labelledby="table">
									<TableHeader>
										<TableColumn>الكشف</TableColumn>
										<TableColumn>المبلغ</TableColumn>
										<TableColumn>الضريبة</TableColumn>
										<TableColumn>صافي المبلغ</TableColumn>
										<TableColumn></TableColumn>
									</TableHeader>
									<TableBody>
										{added &&
											added.map((item) => {
												const currentRow = added.find(
													(el) => el.id === item.id
												);
												console.log(`currentRow`, currentRow);
												return (
													<TableRow key={item.id}>
														<TableCell>
															<Autocomplete
																className="max-w-xs "
																label="الكشف"
																onSelectionChange={(dueList_id) => {
																	dueListIdChangeHandler(dueList_id, item.id);
																}}
																size="sm"
																value={
																	currentRow?.dueList_id === null
																		? null
																		: currentRow
																}
																isClearable={false}
															>
																{duesLists.map((el) => (
																	<AutocompleteItem
																		key={el.id}
																		dir="rtl"
																		className="text-right"
																		isDisabled={el.disabled}
																	>
																		{`${el.id} - ${el.title}`}
																	</AutocompleteItem>
																))}
															</Autocomplete>
														</TableCell>

														<TableCell>{item.amount}</TableCell>
														<TableCell>{item.tax}</TableCell>
														<TableCell>{item.net_amount}</TableCell>
														<TableCell>
															<Tooltip content="حذف" placement="top">
																<Button
																	isIconOnly
																	variant="flat"
																	className="bg-danger text-white"
																	onPress={() =>
																		setAdded((prev) =>
																			prev.filter((el) => el.id !== item.id)
																		)
																	}
																>
																	<Trash />
																</Button>
															</Tooltip>
														</TableCell>
													</TableRow>
												);
											})}
										{added.length > 0 && (
											<TableRow>
												<TableCell className=" font-bold text-large">
													الاجمالي
												</TableCell>

												<TableCell className=" font-bold text-large">
													{amount}
												</TableCell>
												<TableCell className=" font-bold text-large">
													{tax}
												</TableCell>
												<TableCell
													colSpan={2}
													className=" font-bold text-large"
												>
													{netAmount}
												</TableCell>
											</TableRow>
										)}
									</TableBody>
								</Table>
								<Row>
									<Button appearance="primary" onClick={addHandler}>
										إضافة
									</Button>
								</Row>
							</CardBody>
						</Card>
					)}
				</div>
			</form>
		</div>
	);
};

export default ReceiveEditFormPage;
