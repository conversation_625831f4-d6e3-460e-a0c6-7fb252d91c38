import React, { useContext, useEffect, useState } from "react";
import TopBar from "../../components/TopBar/TopBar";
import { useSearchParams } from "react-router-dom";
import { useMutation, useQuery, useQueryClient } from "react-query";
import {
	deleteDuesLists,
	getAllDuesLists,
	getAllDueTypes,
	getDuesListById,
	updateDuesListState,
} from "../../api/serverApi";
import { toast } from "react-toastify";

import {
	Modal,
	ModalContent,
	ModalHeader,
	ModalBody,
	ModalFooter,
	useDisclosure,
	Card,
	CardHeader,
	CardBody,
	Button,
	Select,
	SelectItem,
	Table,
	TableHeader,
	TableColumn,
	TableBody,
	TableRow,
	TableCell,
	Dropdown,
	DropdownTrigger,
	DropdownMenu,
	DropdownItem,
	Pagination,
	RadioGroup,
	Radio,
	Chip,
} from "@heroui/react";

import {
	Plus,
	Trash,
	Printer,
	Check,
	X,
	DotsVertical,
	Edit,
	Refresh,
} from "@mynaui/icons-react";
import useNavigateWithQuery from "@/hooks/useNavigateWithQuery";

const DuesListsPage = () => {
	// hooks
	const navigate = useNavigateWithQuery();
	const queryClient = useQueryClient();
	const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure();

	// state
	// const [selectedRows, setSelectedRows] = useState([]);
	// const [selectedRowsData, setSelectedRowsData] = useState([]);
	const [type, setType] = useState("");
	const [searchParams, setSearchParams] = useSearchParams();
	const page = parseInt(searchParams.get("page")) || 1;
	const rowsPerPage = parseInt(searchParams.get("rowsPerPage")) || 5;
	const [pages, setPages] = useState(1);
	const [total, setTotal] = useState("");
	const [selectedItems, setSelectedItems] = useState(new Set());
	const [selectedItemsArr, setSelectedItemsArr] = useState([]);
	const [duesListToFetch, setDuesListToFetch] = useState(null);
	const [duesLists, setDuesLists] = useState([]);
	const [state, setState] = useState("");
	// dialog mode/payload (no JSX in state)
	// modes: "selectType", "delete", "confirmChangeState", "sendSMS"
	const [dialogMode, setDialogMode] = useState("");
	const [dialogPayload, setDialogPayload] = useState(null);
	// queries
	useQuery({
		queryKey: ["duesLists", page - 1, rowsPerPage, "", ""],
		queryFn: getAllDuesLists,
		select: (res) => {
			return res.data;
		},
		onSuccess: (data) => {
			setDuesLists(data.duesLists);
			setPages(Math.ceil(data.total / rowsPerPage));
			setTotal(data.total);
		},
	});

	const { data: dueTypes } = useQuery({
		queryKey: ["dueTypes"],
		queryFn: getAllDueTypes,
		select: (res) => res.data.types.map((el) => el),
	});
	useQuery({
		queryKey: ["duesListData", duesListToFetch?.id],
		queryFn: getDuesListById,
		select: (res) => {
			return res.data.duesList;
		},
		onSuccess: (data) => {
			const dataToPrint = `${data.date.split("T")[0].replace(/-/g, "/")}`;
			let reportTemplate = "";
			if (data.type === "غذاءات") {
				reportTemplate = "foodDueList";
			} else {
				reportTemplate = "otherDueList";
			}
			navigate("./print", {
				state: {
					data: {
						...data,
						date: dataToPrint,
					},
					reportTemplate,
				},
			});
		},
		enabled: !!duesListToFetch?.id,
	});

	// const { refetch } = useQuery({
	// 	queryKey: ["duesByListId", selectedRows],
	// 	queryFn: getDuesByDuesListsIds,
	// 	select: (res) => res.data.dues,
	// 	enabled: false,
	// 	onSuccess: (data) => {
	// 		setDialogMode("sendSMS");
	// 		setDialogPayload({ data });
	// 		onOpen();
	// 	},
	// });

	// mutations
	const updateMutation = useMutation({
		mutationFn: updateDuesListState,
		onSuccess: () => {
			toast.success("تم تعديل الحالة بنجاح", {
				position: toast.POSITION.TOP_CENTER,
			});
			queryClient.invalidateQueries({ queryKey: ["duesLists"] });
			onClose();
			setDialogMode("");
			setState("");
			setSelectedItems(new Set());
			setSelectedItemsArr([]);
		},
		onError: (err) => {
			toast.error(err?.response?.data?.message || "حدث خطأ", {
				position: toast.POSITION.TOP_CENTER,
			});
			onClose();
			setDialogMode("");
			setState("");
		},
	});

	// const SendMessages = useMutation({
	// 	mutationFn: sendMessages,
	// 	onSuccess: () => {
	// 		toast.success("تم الارسال بنجاح", {
	// 			position: toast.POSITION.TOP_CENTER,
	// 		});
	// 		setSelectedRows([]);
	// 		setSelectedRowsData([]);
	// 		onClose();
	// 		setDialogMode("");
	// 		setDialogPayload(null);
	// 	},
	// 	onError: (err) => {
	// 		toast.error(err?.response?.data?.message || "حدث خطأ", {
	// 			position: toast.POSITION.TOP_CENTER,
	// 		});
	// 		onClose();
	// 		setDialogMode("");
	// 		setDialogPayload(null);
	// 	},
	// });

	// const approveMutation = useMutation({
	// 	mutationFn: approveDuesList,
	// 	onSuccess: () => {
	// 		toast.success("تمت العملية بنجاح", {
	// 			position: toast.POSITION.TOP_CENTER,
	// 		});
	// 		queryClient.invalidateQueries({ queryKey: ["duesLists"] });
	// 		setSelectedRows([]);
	// 		onClose();
	// 		setDialogMode("");
	// 		setDialogPayload(null);
	// 	},
	// 	onError: (err) => {
	// 		toast.error(err?.response?.data?.message || "حدث خطأ", {
	// 			position: toast.POSITION.TOP_CENTER,
	// 		});
	// 		onClose();
	// 		setDialogMode("");
	// 		setDialogPayload(null);
	// 	},
	// });

	const deleteMutation = useMutation({
		mutationFn: deleteDuesLists,
		onSuccess: () => {
			toast.success("تم الحذف بنجاح", { position: toast.POSITION.TOP_CENTER });
			queryClient.invalidateQueries({ queryKey: ["duesLists"] });
			onClose();
			setDialogMode("");
			// setDialogPayload(null);
		},
		onError: (err) => {
			toast.error(err?.response?.data?.message || "حدث خطأ", {
				position: toast.POSITION.TOP_CENTER,
			});
			onClose();
			setDialogMode("");
			// setDialogPayload(null);
		},
	});

	// logic

	const sendSMSHandler = () => {
		// refetch();
	};

	// useEffect(() => {
	// 	if (duesLists && duesLists.length > 0) {
	// 		const selectedIDs = new Set(selectedRows);
	// 		const selectedRowData = duesLists.filter((row) =>
	// 			selectedIDs.has(row.id)
	// 		);
	// 		setSelectedRowsData(selectedRowData);
	// 	} else {
	// 		setSelectedRowsData([]);
	// 	}
	// }, [selectedRows, duesLists]);
	// function
	const onRowsPerPageChange = React.useCallback(
		(e) => {
			const newRowsPerPage = Number(e.target.value);
			setSearchParams({
				page: "1", // Reset to first page when changing rows per page
				rowsPerPage: newRowsPerPage.toString(),
			});
		},
		[setSearchParams]
	);
	const handlePageChange = (newPage) => {
		setSearchParams({
			page: newPage.toString(),
			rowsPerPage: rowsPerPage.toString(),
		});
	};
	useEffect(() => {
		console.log(`foo`, page, rowsPerPage, total);
		const arrayOfIds = Array.from(selectedItems);
		setSelectedItemsArr(arrayOfIds.map((el) => +el));
	}, [selectedItems, duesLists]);

	return (
		<div className="w-full h-full overflow-auto ">
			{/* Modal */}
			<Modal isOpen={isOpen} onOpenChange={onOpenChange} size="xl">
				<ModalContent>
					{() => (
						<>
							<ModalHeader className="flex flex-col gap-1">
								{dialogMode === "selectType" && "نوع الاستحقاق"}
								{dialogMode === "delete" ||
									("groupDelete" && "حذف كشف مستحقات")}
								{dialogMode === "confirmChangeState" && "صرف مستحقات"}
								{dialogMode === "sendSMS" && "ارسال رسائل SMS"}
								{dialogMode === "changeState" && "تغيير حالة الكشف"}
							</ModalHeader>
							<ModalBody>
								{dialogMode === "selectType" && (
									<div className="flex flex-col gap-2">
										<Select
											label="نوع الاستحقاق"
											selectedKeys={type ? [type] : []}
											onSelectionChange={(keys) => {
												const val = Array.from(keys)[0] || "";
												setType(val);
											}}
											placeholder="اختر نوع الاستحقاق"
											size="sm"
										>
											{dueTypes?.map((el) => (
												<SelectItem key={el.name}>{el.name}</SelectItem>
											))}
										</Select>
									</div>
								)}
								{dialogMode === "delete" && (
									<div>
										{`هل انت متاكد من حذف كشف المستحقات رقم ${dialogPayload?.id} بعنوان ${dialogPayload?.title} ؟`}
									</div>
								)}
								{dialogMode === "groupDelete" && (
									<div dir="rtl">
										<div>هل انت متأكد من حذف الكشوفات التالية:</div>
										<ol>
											{selectedItemsArr.map((el) => {
												const item = duesLists.filter(
													(ele) => ele.id === el
												)[0];

												return (
													<li key={item.id}>
														{item.id} - {item.title}
													</li>
												);
											})}
										</ol>
									</div>
								)}
								{dialogMode === "changeState" && (
									<>
										<div className="flex flex-col gap-2">
											<RadioGroup
												label="حالة الكشف"
												orientation="horizontal"
												dir="rtl"
												value={state}
												onValueChange={setState}
											>
												<Radio
													isDisabled={duesLists.some(
														(el) =>
															selectedItems.has(`${el.id}`) &&
															(el.state === "قابل للدفع" || el.state === "مغلق")
													)}
													value="تحت المراجعة"
												>
													تحت المراجعة
												</Radio>
												<Radio
													isDisabled={duesLists.some(
														(el) =>
															selectedItems.has(`${el.id}`) &&
															(el.state === "قابل للدفع" || el.state === "مغلق")
													)}
													value="تمت المراجعة"
												>
													تمت المراجعة
												</Radio>
												<Radio
													isDisabled={duesLists.some(
														(el) =>
															selectedItems.has(`${el.id}`) &&
															(el.state === "تمت المراجعة" ||
																el.state === "تحت المراجعة")
													)}
													value="مغلق"
												>
													مغلق
												</Radio>
											</RadioGroup>
										</div>
									</>
								)}
							</ModalBody>
							<ModalFooter>
								{(dialogMode === "selectType" ||
									dialogMode === "delete" ||
									dialogMode === "groupDelete" ||
									dialogMode === "changeState" ||
									dialogMode === "sendSMS") && (
									<div className="flex gap-3">
										<Button
											variant="flat"
											onPress={() => {
												setState("");
												onClose();
											}}
											color="warning"
											startContent={<X />}
										>
											الغاء
										</Button>
										{dialogMode === "selectType" && (
											<Button
												color="primary"
												onPress={() => {
													if (type === "مكافآت") {
														navigate("./FinancialBonusDuesFormPage");
													}
													if (type === "رعاية طبية") {
														navigate("./MedicalCareDuesFormPage");
													}
													onClose();
												}}
												isDisabled={!type}
												startContent={<Check />}
											>
												تأكيد
											</Button>
										)}
										{dialogMode === "delete" && (
											<Button
												color="danger"
												onPress={() => {
													if (dialogPayload?.id)
														deleteMutation.mutate(dialogPayload.id);
												}}
												startContent={<Trash />}
											>
												تأكيد
											</Button>
										)}
										{dialogMode === "groupDelete" && (
											<Button
												color="danger"
												onPress={() => {
													deleteMutation.mutate(selectedItemsArr);
												}}
												startContent={<Trash />}
											>
												تأكيد
											</Button>
										)}
										{dialogMode === "changeState" && (
											<Button
												color="primary"
												onPress={() => {
													updateMutation.mutate({
														duesLists: selectedItemsArr,
														state: state,
													});
												}}
												isDisabled={!state}
												startContent={<Check />}
											>
												تأكيد
											</Button>
										)}
									</div>
								)}
							</ModalFooter>
						</>
					)}
				</ModalContent>
			</Modal>
			{/* TopBar */}
			<TopBar
				right={
					<Button
						color="primary"
						onPress={() => {
							setDialogMode("selectType");
							onOpen();
						}}
						startContent={<Plus />}
					>
						اضافة
					</Button>
				}
			/>

			{/* Main content */}
			<div className="w-full p-5 pb-16">
				{duesLists.length > 0 && (
					<Card>
						<CardHeader className="bg-primary text-default-50 font-bold text-medium">
							المستحقات
						</CardHeader>
						<CardBody>
							<div className="flex justify-end gap-3 mb-4">
								<Button
									onPress={() => {
										setDialogMode("changeState");
										onOpen();
									}}
									color="primary"
									startContent={<Refresh />}
									isDisabled={selectedItemsArr.length === 0}
								>
									تغيير حالة الكشف
								</Button>
								<Button
									onPress={() => {
										setDialogMode("groupDelete");
										setDialogPayload({
											ids: selectedItemsArr,
										});
										onOpen();
									}}
									color="danger"
									startContent={<Trash />}
									isDisabled={selectedItemsArr.length === 0}
								>
									حذف
								</Button>
							</div>

							<Table
								aria-labelledby="table"
								bottomContent={
									<div className="py-2 px-2 flex justify-between items-center">
										<span className="text-default-400 text-small">
											الاجمالي {total} حركة
										</span>
										<Pagination
											showControls
											showShadow
											color="primary"
											page={page}
											total={pages}
											onChange={handlePageChange}
										/>
										<label className="flex items-center text-default-400 text-small">
											النتائج لكل صفحة:
											<select
												className="bg-transparent outline-none text-default-400 text-small"
												onChange={onRowsPerPageChange}
												value={rowsPerPage}
											>
												<option value="5">5</option>
												<option value="10">10</option>
												<option value="15">15</option>
											</select>
										</label>
									</div>
								}
								selectionMode="multiple"
								selectedKeys={selectedItems}
								onSelectionChange={(keys) => {
									if (typeof keys === "string" && keys === "all") {
										setSelectedItems(
											new Set(duesLists.map((item) => `${item.id}`))
										);
									} else {
										setSelectedItems(new Set(keys));
									}
								}}
								bottomContentPlacement="outside"
							>
								<TableHeader>
									<TableColumn>م</TableColumn>
									<TableColumn>التاريخ</TableColumn>
									<TableColumn>العنوان</TableColumn>
									<TableColumn>صافي القيمة</TableColumn>
									<TableColumn>الحالة</TableColumn>
									<TableColumn>النوع</TableColumn>
									<TableColumn>خيارات</TableColumn>
								</TableHeader>
								<TableBody>
									{duesLists &&
										duesLists.map((duesList) => {
											const disabledActions = [];
											let chip = "";
											if (
												duesList.state === "تمت المراجعة" ||
												duesList.state === "قابل للدفع" ||
												duesList.state === "مغلق"
											) {
												disabledActions.push("delete");
												disabledActions.push("edit");
											}
											if (duesList.is_paid && duesList.state === "قابل للدفع") {
												chip = (
													<Chip color="success" variant="solid">
														مُستلمة
													</Chip>
												);
											} else if (duesList.state === "قابل للدفع") {
												chip = (
													<Chip color="primary" variant="solid">
														{duesList.state}
													</Chip>
												);
											} else if (duesList.state === "تمت المراجعة") {
												chip = (
													<Chip className="bg-success-100" variant="solid">
														{duesList.state}
													</Chip>
												);
											} else if (duesList.state === "تحت المراجعة") {
												chip = (
													<Chip color="warning" variant="solid">
														{duesList.state}
													</Chip>
												);
											} else if (duesList.state === "مغلق") {
												chip = (
													<Chip color="default" variant="solid">
														{duesList.state}
													</Chip>
												);
											}
											return (
												<TableRow
													key={duesList.id}
													className={
														duesList.is_paid && duesList.state === "قابل للدفع"
															? "bg-green-200"
															: ""
													}
												>
													<TableCell>{duesList.id}</TableCell>
													<TableCell>{duesList.date}</TableCell>
													<TableCell>{duesList.title}</TableCell>
													<TableCell>{duesList.net_amount}</TableCell>
													<TableCell>{chip}</TableCell>
													<TableCell>{duesList.type}</TableCell>
													<TableCell>
														<div className="relative flex justify-center items-center gap-2">
															<Dropdown>
																<DropdownTrigger>
																	<Button isIconOnly variant="light">
																		<DotsVertical size="40" />
																	</Button>
																</DropdownTrigger>
																<DropdownMenu
																	disabledKeys={disabledActions}
																	onAction={(key) => {
																		console.log(`key`, key);
																		if (key === "edit") {
																			if (duesList.type === "مكافآت") {
																				navigate(
																					"./FinancialBonusDuesFormPage/edit",
																					{
																						state: { id: duesList.id },
																					}
																				);
																			} else if (
																				duesList.type === "رعاية طبية"
																			) {
																				navigate(
																					"./MedicalCareDuesFormPage/edit",
																					{
																						state: { id: duesList.id },
																					}
																				);
																			}
																		}
																		if (key === "print") {
																			setDuesListToFetch({
																				id: duesList.id,
																			});
																		}

																		if (key === "delete") {
																			setDialogMode("delete");
																			setDialogPayload({
																				id: duesList.id,
																				title: duesList.title,
																			});
																			onOpen();
																		}
																	}}
																>
																	<DropdownItem
																		key="edit"
																		startContent={<Edit />}
																	>
																		تعديل
																	</DropdownItem>
																	<DropdownItem
																		key="print"
																		startContent={<Printer />}
																	>
																		طباعة
																	</DropdownItem>
																	<DropdownItem
																		key="delete"
																		className="text-danger"
																		color="danger"
																		startContent={<Trash />}
																	>
																		حذف
																	</DropdownItem>
																</DropdownMenu>
															</Dropdown>
														</div>
													</TableCell>
												</TableRow>
											);
										})}
								</TableBody>
							</Table>
						</CardBody>
					</Card>
				)}
			</div>
		</div>
	);
};

export default DuesListsPage;
