import React, { useContext, useEffect, useRef, useState } from "react";
import { useMutation, useQuery, useQueryClient } from "react-query";
import TopBar from "../../components/TopBar/TopBar";
import { useSearchParams } from "react-router-dom";
import { deleteReceives, getAllReceives } from "../../api/serverApi";

import { toast } from "react-toastify";
import { AuthContext } from "../../store/auth-context";
import {
	Modal,
	ModalContent,
	ModalHeader,
	ModalBody,
	ModalFooter,
	useDisclosure,
	Card,
	CardHeader,
	CardBody,
	Button,
	Table,
	TableHeader,
	TableColumn,
	TableBody,
	TableRow,
	TableCell,
	Dropdown,
	DropdownTrigger,
	DropdownMenu,
	DropdownItem,
	Pagination,
} from "@heroui/react";

import {
	Plus,
	Trash,
	Printer,
	Check,
	X,
	DotsVertical,
	Edit,
} from "@mynaui/icons-react";
import useNavigateWithQuery from "@/hooks/useNavigateWithQuery";
const ReceivesPage = () => {
	//hooks
	const navigate = useNavigateWithQuery();
	const queryClient = useQueryClient();
	const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure();
	//states
	const [searchParams, setSearchParams] = useSearchParams();
	const page = parseInt(searchParams.get("page")) || 1;
	const rowsPerPage = parseInt(searchParams.get("rowsPerPage")) || 5;
	const [pages, setPages] = useState(1);
	const [total, setTotal] = useState("");
	const [selectedItems, setSelectedItems] = useState(new Set());
	const [selectedItemsArr, setSelectedItemsArr] = useState([]);
	const [dialogMode, setDialogMode] = useState("");
	const [receives, setReceives] = useState([]);
	const [dialogPayload, setDialogPayload] = useState(null);
	//queries

	useQuery({
		queryKey: ["receives", page - 1, rowsPerPage],
		queryFn: getAllReceives,
		select: (res) => {
			return res.data;
		},
		onSuccess: (data) => {
			setReceives(data.receives);
			setPages(Math.ceil(data.total / rowsPerPage));
			setTotal(data.total);
		},
	});
	const deleteMutation = useMutation({
		mutationFn: deleteReceives,
		onSuccess: () => {
			toast.success("تم الحذف بنجاح", {
				position: toast.POSITION.TOP_CENTER,
			});
			queryClient.invalidateQueries({ queryKey: ["receives"] });
			setDialogMode("");
			setSelectedItems(new Set());
			onClose();
		},
		onError: (err) => {
			toast.error(err.response.data.message, {
				position: toast.POSITION.TOP_CENTER,
			});
			setDialogMode("");
			onClose();
		},
	});
	//functions
	const onRowsPerPageChange = React.useCallback(
		(e) => {
			const newRowsPerPage = Number(e.target.value);
			setSearchParams({
				page: "1", // Reset to first page when changing rows per page
				rowsPerPage: newRowsPerPage.toString(),
			});
		},
		[setSearchParams]
	);
	const handlePageChange = (newPage) => {
		setSearchParams({
			page: newPage.toString(),
			rowsPerPage: rowsPerPage.toString(),
		});
	};
	useEffect(() => {
		const arrayOfIds = Array.from(selectedItems);
		setSelectedItemsArr(arrayOfIds.map((el) => +el));
	}, [selectedItems, receives]);

	return (
		<div className="w-full h-full overflow-auto ">
			<Modal isOpen={isOpen} onOpenChange={onOpenChange} size="xl">
				<ModalContent>
					{() => (
						<>
							<ModalHeader className="flex flex-col gap-1 text-center bg-primary text-default-50">
								{dialogMode === "delete" && "حذف سندات قبض"}
							</ModalHeader>
							<ModalBody dir="rtl">
								{dialogMode === "delete" && (
									<div>
										{`هل انت متاكد من حذف كشف المستحقات رقم ${dialogPayload?.id} بصافي مبلغ  ${dialogPayload?.net_amount} ؟`}
									</div>
								)}
								{dialogMode === "deleteGroup" && (
									<div>
										هل انت متاكد من حذف السندات المحددة؟
										<ol>
											{selectedItemsArr.map((el) => (
												<li key={el}>{el}</li>
											))}
										</ol>
									</div>
								)}
							</ModalBody>
							<ModalFooter>
								<Button
									variant="flat"
									onPress={() => {
										onClose();
									}}
									color="danger"
									startContent={<X />}
								>
									الغاء
								</Button>
								{dialogMode === "delete" && (
									<Button
										color="danger"
										onPress={() => {
											console.log(`foo`, dialogPayload?.id);
											if (dialogPayload?.id)
												deleteMutation.mutate([dialogPayload.id]);
										}}
										startContent={<Trash />}
									>
										تأكيد
									</Button>
								)}
								{dialogMode === "groupDelete" && (
									<Button
										color="danger"
										onPress={() => {
											deleteMutation.mutate(selectedItemsArr);
										}}
										startContent={<Trash />}
									>
										تأكيد
									</Button>
								)}
								{/* <Button
									color="primary"
									onPress={() => {
										deleteMutation.mutate(selectedItemsArr);
									}}
									startContent={<Check />}
								>
									تأكيد
								</Button> */}
							</ModalFooter>
						</>
					)}
				</ModalContent>
			</Modal>
			<TopBar
				right={
					<>
						<Button
							onPress={() => {
								navigate("./add");
							}}
							color="primary"
							startContent={<Plus />}
						>
							قبض
						</Button>
					</>
				}
			/>
			<div className="w-full p-5 pb-16">
				{receives.length > 0 && (
					<Card>
						<CardHeader className="bg-primary text-default-50 font-bold text-medium">
							القبوض
						</CardHeader>
						<CardBody>
							<div className="flex justify-end gap-3 mb-4">
								<Button
									onPress={() => {
										setDialogMode("delete");
										onOpen();
									}}
									color="danger"
									startContent={<Trash />}
									isDisabled={selectedItemsArr.length === 0}
								>
									حذف
								</Button>
							</div>

							<Table
								aria-labelledby="table"
								bottomContent={
									<div className="py-2 px-2 flex justify-between items-center">
										<span className="text-default-400 text-small">
											الاجمالي {total} حركة
										</span>
										<Pagination
											showControls
											showShadow
											color="primary"
											page={page}
											total={pages}
											onChange={handlePageChange}
										/>
										<label className="flex items-center text-default-400 text-small">
											النتائج لكل صفحة:
											<select
												className="bg-transparent outline-none text-default-400 text-small"
												onChange={onRowsPerPageChange}
												value={rowsPerPage}
											>
												<option value="5">5</option>
												<option value="10">10</option>
												<option value="15">15</option>
											</select>
										</label>
									</div>
								}
								selectionMode="multiple"
								selectedKeys={selectedItems}
								onSelectionChange={(keys) => {
									if (typeof keys === "string" && keys === "all") {
										setSelectedItems(
											new Set(receives.map((item) => `${item.id}`))
										);
									} else {
										setSelectedItems(new Set(keys));
									}
								}}
								bottomContentPlacement="outside"
							>
								<TableHeader>
									<TableColumn>م</TableColumn>
									<TableColumn>جهة الاستلام</TableColumn>
									<TableColumn>المبلغ</TableColumn>
									<TableColumn>الضريبة</TableColumn>
									<TableColumn>صافي المبلغ</TableColumn>
									<TableColumn>خيارات</TableColumn>
								</TableHeader>
								<TableBody>
									{receives &&
										receives.map((receive) => {
											return (
												<TableRow key={receive.id}>
													<TableCell>{receive.id}</TableCell>
													<TableCell>{receive.money_from}</TableCell>
													<TableCell>{receive.amount}</TableCell>
													<TableCell>{receive.tax}</TableCell>
													<TableCell>{receive.net_amount}</TableCell>
													<TableCell>
														<div className="relative flex justify-center items-center gap-2">
															<Dropdown>
																<DropdownTrigger>
																	<Button isIconOnly variant="light">
																		<DotsVertical size="40" />
																	</Button>
																</DropdownTrigger>
																<DropdownMenu
																	onAction={(key) => {
																		if (key === "edit") {
																			navigate("./edit", {
																				state: { id: receive.id },
																			});
																		}
																		if (key === "print") {
																			navigate("./print", {
																				state: { item: receive },
																			});
																		}

																		if (key === "delete") {
																			setDialogMode("delete");
																			setDialogPayload({
																				id: receive.id,
																				net_amount: receive.net_amount,
																			});
																			onOpen();
																		}
																	}}
																>
																	{/* <DropdownItem
																		key="edit"
																		startContent={<Edit />}
																	>
																		تعديل
																	</DropdownItem> */}
																	<DropdownItem
																		key="print"
																		startContent={<Printer />}
																	>
																		طباعة
																	</DropdownItem>
																	<DropdownItem
																		key="delete"
																		className="text-danger"
																		color="danger"
																		startContent={<Trash />}
																	>
																		حذف
																	</DropdownItem>
																</DropdownMenu>
															</Dropdown>
														</div>
													</TableCell>
												</TableRow>
											);
										})}
								</TableBody>
							</Table>
						</CardBody>
					</Card>
				)}
			</div>
		</div>
	);
};

export default ReceivesPage;
