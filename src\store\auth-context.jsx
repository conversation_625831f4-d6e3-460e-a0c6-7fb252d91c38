import { createContext, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { serverApi } from "../api/axios";

export const AuthContext = createContext({
	currUser: null,
	permissions: null,
	login: () => {},
	logout: () => {},
});

const AuthContextProvider = ({ children }) => {
	AuthContextProvider.propTypes = {
		children: PropTypes.node.isRequired,
	};

	const [token, setToken] = useState(() => localStorage.getItem("token"));
	const [year, setYear] = useState(() => {
		const storedYear = localStorage.getItem("year");
		return storedYear ? storedYear : null;
	});
	const [currUser, setCurrUser] = useState(() =>
		JSON.parse(localStorage.getItem("user"))
	);
	const [permissions, setPermissions] = useState(() =>
		JSON.parse(localStorage.getItem("permissions"))
	);

	// Sync token with localStorage and axios headers
	useEffect(() => {
		if (token) {
			localStorage.setItem("token", token);
			serverApi.defaults.headers.common["Authorization"] = `Bearer ${token}`;
		} else {
			localStorage.removeItem("token");
			delete serverApi.defaults.headers.common["Authorization"];
		}
	}, [token]);
	// Sync year with localStorage
	useEffect(() => {
		if (year) {
			localStorage.setItem("year", year);
		} else {
			localStorage.removeItem("year");
		}
	}, [year]);
	// Sync currUser with localStorage
	useEffect(() => {
		if (currUser) {
			localStorage.setItem("user", JSON.stringify(currUser));
		} else {
			localStorage.removeItem("user");
		}
	}, [currUser]);

	// Sync permissions with localStorage
	useEffect(() => {
		if (permissions) {
			localStorage.setItem("permissions", JSON.stringify(permissions));
		} else {
			localStorage.removeItem("permissions");
		}
	}, [permissions]);

	// Login function sets token, user and permissions
	const login = async (data) => {
		try {
			serverApi.defaults.withCredentials = true;

			const res = await serverApi.post("/auth/login", data, {
				withCredentials: true,
				headers: { crossDomain: true, "Content-Type": "application/json" },
			});

			setToken(res.data.token);
			setCurrUser(res.data.user);
			setPermissions(res.data.permissions);
		} catch (error) {
			throw error;
		}
	};

	// Logout clears all data and axios header
	const logout = () => {
		setToken(null);
		setYear(null);
		setCurrUser(null);
		setPermissions(null);
	};

	const value = {
		currUser,
		permissions,
		login,
		logout,
		year,
		setYear,
	};

	return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export default AuthContextProvider;
