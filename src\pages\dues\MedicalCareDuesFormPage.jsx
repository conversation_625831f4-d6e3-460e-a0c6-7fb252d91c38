import { useState } from "react";
import { useMutation, useQuery } from "react-query";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";

import TopBar from "../../components/TopBar/TopBar";
import Row from "../../UI/row/Row";

import {
	Button,
	Card,
	CardHeader,
	CardBody,
	Table,
	TableHeader,
	TableColumn,
	TableBody,
	TableRow,
	TableCell,
	Input,
	Select,
	SelectItem,
} from "@heroui/react";
import * as XLSX from "xlsx";
import { X, Save, Upload } from "@mynaui/icons-react";

import {
	addDuesList,
	getAllBeneficiaries,
	getAllClauses,
	getDuesListById,
} from "../../api/serverApi";

const MedicalCareDuesFormPage = () => {
	// hooks
	const navigate = useNavigate();
	// state
	const [title, setTitle] = useState("");
	const [listNumber, setListNumber] = useState("");
	const [excelFile, setExcelFile] = useState(null);
	const [excelData, setExcelData] = useState(null);
	const [total, setTotal] = useState(0);
	const [duesListToFetch, setDuesListToFetch] = useState(null);
	const [deduction, setDeduction] = useState(0);
	const [netAmount, setNetAmount] = useState(0);
	const [clause, setClause] = useState("");
	const [date, setDate] = useState(new Date().toISOString().split("T")[0]);
	// queries
	const { data: beneficiaries } = useQuery({
		queryKey: ["beneficiaries"],
		queryFn: getAllBeneficiaries,
		select: (res) => res.data.beneficiaries.map((el) => ({ ...el })),
	});
	const { data: clauses } = useQuery({
		queryKey: ["clauses"],
		queryFn: getAllClauses,
		select: (res) => res.data.clauses,
	});
	useQuery({
		queryKey: ["duesListData", duesListToFetch?.id],
		queryFn: getDuesListById,
		select: (res) => {
			return res.data.duesList;
		},
		onSuccess: (data) => {
			const dataToPrint = `${data.date.split("T")[0].replace(/-/g, "/")}`;
			let reportTemplate = "";
			if (data.type === "غذاءات") {
				reportTemplate = "foodDueList";
			} else {
				reportTemplate = "otherDueList";
			}
			console.log(`foo`, {
				...data,
				date: dataToPrint,
			});

			data.dues.forEach((el) => {
				el.amountAfterDeduction = +el.amount - +el.deduction;
				el.tax = +el.tax;
			});

			navigate("./../print", {
				state: {
					data: {
						...data,
						date: dataToPrint,
					},
					reportTemplate,
				},
			});
		},
		enabled: !!duesListToFetch?.id,
	});
	// mutations
	const saveMutation = useMutation({
		mutationFn: addDuesList,
		onSuccess: (res) => {
			toast.success("تم إضافة المستحقات بنجاح", {
				position: toast.POSITION.TOP_CENTER,
			});
			setDuesListToFetch(res.data.duesList);
		},
		onError: (err) => {
			toast.error(err.response?.data?.message || "حدث خطأ", {
				position: toast.POSITION.TOP_CENTER,
			});
		},
	});

	// effects
	// useEffect(() => {
	// 	let total = 0;
	// 	let tax = 0;
	// 	let net_amount = 0;
	// 	const updatedBeneficiaries = beneficiaries.map((beneficiary) => {
	// 		const match = addedEmployees.find(
	// 			(employee) => +employee.beneficiary_id === +beneficiary.id
	// 		);
	// 		if (match) return { ...beneficiary, disabled: true };
	// 		return { ...beneficiary, disabled: false };
	// 	});
	// 	addedEmployees.forEach((el) => {
	// 		total = total + +el.amount;
	// 		tax = tax + +el.tax;
	// 		net_amount = net_amount + +el.net_amount;
	// 	});

	// 	setAmount(total);
	// 	setTax(tax);
	// 	setNetAmount(net_amount);
	// 	setBeneficiaries(updatedBeneficiaries);
	// }, [addedEmployees]);

	// handlers
	const titleChangeHandler = (e) => setTitle(e.target.value);
	const uploadExcelFileHandler = () => {
		if (excelFile) {
			const reader = new FileReader();
			reader.onload = (e) => {
				let totalAmount = 0;
				let deductionAmount = 0;
				let totalNetAmount = 0;
				const data = e.target.result;
				const workbook = XLSX.read(data, { type: "binary" });
				const sheetName = workbook.SheetNames[0];
				const worksheet = workbook.Sheets[sheetName];

				var range = XLSX.utils.decode_range(worksheet["!ref"]);
				const datarange = {
					s: { r: 1, c: 0 },
					e: { r: range.e.r - 1, c: range.e.c },
				};
				const titleCell = worksheet["B1"];
				const listNumberCell = worksheet["E1"];
				const dateCell = worksheet["G1"];
				const titleValue = XLSX.utils.format_cell(titleCell, worksheet["B1"]);
				const listNumberValue = XLSX.utils.format_cell(
					listNumberCell,
					worksheet["E1"]
				);
				const dateValue = XLSX.utils.format_cell(dateCell, worksheet["G1"]);
				setTitle(titleValue);
				setListNumber(+listNumberValue);
				console.log(`listNumberValue`, listNumberValue);

				// var numRows = range.e.r - range.s.r + 1;
				const json = XLSX.utils.sheet_to_json(worksheet, { range: datarange });

				json.map(
					(el) => (deductionAmount = deductionAmount + el["اجمالي الاستقطاعات"])
				);
				json.map((el) => (totalAmount = totalAmount + el["اجمالي المستحقات"]));
				json.map((el) => (totalNetAmount = totalNetAmount + el["الصافي"]));
				const newData = json
					.map((jsonObj) => {
						const matchingBenficary = beneficiaries.find(
							(bObj) => bObj.financial_num === jsonObj["الرقم المالي"]
						);
						if (matchingBenficary) {
							return {
								id: jsonObj["م"],
								beneficiary_id: matchingBenficary.id,
								name: matchingBenficary.name,
								financial_num: matchingBenficary.financial_num,
								amount: jsonObj["اجمالي المستحقات"],
								deduction: jsonObj["اجمالي الاستقطاعات"],
								net_amount: jsonObj["الصافي"],
								tax: 0,
							};
						} else {
							toast.error(
								`لا يوجد مستفيد مرتبط بهذا الرقم المالي ${jsonObj["الرقم المالي"]}`,
								{
									position: toast.POSITION.TOP_CENTER,
								}
							);
							return null;
						}
					})
					.filter(Boolean);
				setExcelData(newData);
				setTotal(totalAmount);
				setDeduction(deductionAmount);
				setNetAmount(totalNetAmount);
			};
			reader.readAsBinaryString(excelFile);
		}
	};

	const onSaveHandler = (e) => {
		e.preventDefault();
		const payload = {
			title: title.replace(/\s+/g, " ").trim(),
			added: excelData,
			date,
			clause,
			type: "رعاية طبية",
			listNumber,
		};
		saveMutation.mutate(payload);
	};

	// table columns

	return (
		<div className="w-full h-full overflow-auto">
			<form onSubmit={onSaveHandler}>
				<TopBar
					right={
						<div className="flex gap-3">
							<Button
								variant="flat"
								color="warning"
								onPress={() => {
									navigate("./..");
								}}
								startContent={<X />}
							>
								الغاء
							</Button>
							<Button
								color="primary"
								type="submit"
								isDisabled={saveMutation.isLoading}
								startContent={<Save />}
							>
								حفظ
							</Button>
						</div>
					}
				/>
				<div className="w-full p-5 pb-16">
					<Card>
						<CardHeader className="bg-primary text-default-50 font-bold text-medium">
							بيانات المستند
						</CardHeader>
						<CardBody>
							<Row flex={[2, 1, 1]}>
								<Input
									label="العنوان"
									value={title}
									onChange={titleChangeHandler}
									placeholder="اكتب العنوان"
									size="sm"
									isRequired
								/>
								<Input
									label="تاريخ الاضافة"
									value={date}
									type="date"
									onChange={(e) => setDate(e.target.value)}
									size="sm"
									isRequired
								/>
								<Select
									value={clause}
									label="البند"
									size="sm"
									onChange={(e) => setClause(e.target.value)}
								>
									{clauses &&
										clauses.map((el) => (
											<SelectItem className="text-right" key={el.id}>
												{el.name}
											</SelectItem>
										))}
								</Select>
							</Row>
							<Row flex={[1, 1, 2, 1]}>
								<div
									style={{
										display: "flex",
										alignItems: "flex-end",
										gap: "10px",
									}}
								>
									<Input
										type="file"
										label="تحديد ملف اكسل"
										onChange={(e) => {
											setExcelData(null);
											setExcelFile(e.target.files[0]);
										}}
										size="sm"
										endContent={<Upload />}
										accept=".xls,.xlsx"
									/>

									<Button color="primary" onPress={uploadExcelFileHandler}>
										رفع
									</Button>
								</div>
								<Input
									type="number"
									value={listNumber}
									size="sm"
									isDisabled
									label="رقم الكشف في نظام الاستحقاق"
								/>

								<></>
							</Row>
						</CardBody>
					</Card>
					{excelData && excelData.length > 0 ? (
						<Card>
							<CardHeader className="bg-primary text-default-50 font-bold text-medium">
								بيانات الموظفين
							</CardHeader>
							<CardBody>
								<Table
									aria-labelledby="employees-table"
									className="min-w-[510px]"
								>
									<TableHeader>
										<TableColumn>م</TableColumn>
										<TableColumn>الرقم المالي</TableColumn>
										<TableColumn>الاسم</TableColumn>
										<TableColumn>اجمالي المستحقات</TableColumn>
										<TableColumn>اجمالي الاستقطاعات</TableColumn>
										<TableColumn>صافي المبلغ</TableColumn>
									</TableHeader>
									<TableBody>
										{excelData.map((item, i) => {
											return (
												<TableRow key={i}>
													<TableCell>{item.id}</TableCell>
													<TableCell>{item.financial_num}</TableCell>
													<TableCell>{item.name}</TableCell>
													<TableCell>{item.amount}</TableCell>
													<TableCell>{item.deduction}</TableCell>
													<TableCell>{item.net_amount}</TableCell>
												</TableRow>
											);
										})}
										<TableRow className="bg-primary text-default-50">
											<TableCell className=" font-bold text-large" colSpan={3}>
												الاجمالي
											</TableCell>
											<TableCell className=" font-bold text-large">
												{total.toLocaleString("en-US", {
													minimumFractionDigits: 2,
													maximumFractionDigits: 2,
												})}
											</TableCell>
											<TableCell className=" font-bold text-large">
												{deduction.toLocaleString("en-US", {
													minimumFractionDigits: 2,
													maximumFractionDigits: 2,
												})}
											</TableCell>
											<TableCell className=" font-bold text-large">
												{netAmount.toLocaleString("en-US", {
													minimumFractionDigits: 2,
													maximumFractionDigits: 2,
												})}
											</TableCell>
										</TableRow>
									</TableBody>
								</Table>
							</CardBody>
						</Card>
					) : null}
				</div>
			</form>
		</div>
	);
};

export default MedicalCareDuesFormPage;
