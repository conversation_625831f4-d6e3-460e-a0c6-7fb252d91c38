import { useContext } from "react";

import Navbar from "./components/navbar/Navbar";

import LoginPage from "./pages/login/LoginPage";

import PropTypes from "prop-types";
import {
	createHashRouter,
	RouterProvider,
	Outlet,
	Navigate,
} from "react-router-dom";
import { AuthContext } from "./store/auth-context";
import DuesListsPage from "./pages/dues/DuesListsPage";
import Search from "./pages/dues/Search";
import BeneficiariesPage from "./pages/beneficiaries/BeneficiariesPage";
import BeneficiaryFormPage from "./pages/beneficiaries/BeneficiaryFormPage";
import HomePage from "./pages/home/<USER>";
import ReceivesPage from "./pages/receive/ReceivesPage";
import ReceiveFormPage from "./pages/receive/ReceiveFormPage";
import ReceiveEditFormPage from "./pages/receive/edit/ReceiveEditFormPage";
import ReceiveTemplate from "./pages/templates/ReceiveTemplate";
import PayTemplate from "./pages/templates/PayTemplate";
import PayPage from "./pages/pay/PayPage";
import AddPayPage from "./pages/pay/AddPayPage";
import UsersPage from "./pages/users/UsersPage";
import UserFormPage from "./pages/users/UserFormPage";
import PaysListPage from "./pages/pay/PaysListPage";
import PayDuesTemplate from "./pages/templates/PayDuesTemplate";
import ReceiveReport from "./pages/reports/ReceiveReport";
import ReceiveReportTemplate from "./pages/templates/ReceiveReportTemplate";

import PayDuesListTemplate from "./pages/templates/PayDuesListTemplate";
import SelectYearPage from "./pages/selectYear/SelectYearPage";
// import HrDuesFormPage from "./pages/dues/HrDuesFormPage";
import DueTypesFormPage from "./pages/dueTypes/DueTypesFormPage";
import DueTypesPage from "./pages/dueTypes/DueTypesPage";
import FinancialBonusDuesFormPage from "./pages/dues/FinancialBonusDuesFormPage";
import FinancialBonusDuesEditFormPage from "./pages/dues/edit/FinancialBonusDuesEditFormPage";
import ReportViewer from "./reports/ReportViewer";
import MedicalCareDuesFormPage from "./pages/dues/MedicalCareDuesFormPage";

function App() {
	const { currUser } = useContext(AuthContext);
	const ProtectedRoute = ({ children }) => {
		ProtectedRoute.propTypes = { children: PropTypes.any };
		if (!currUser) {
			return <Navigate to="/login" />;
		}
		return children;
	};
	const RedirectLogedinUser = ({ children }) => {
		RedirectLogedinUser.propTypes = { children: PropTypes.any };
		if (currUser) {
			return <Navigate to="/" />;
		}
		return children;
	};
	const Layout = () => {
		return (
			<div style={{ height: "100vh", overflow: "hidden" }}>
				<Navbar />
				<div
					style={{
						overflow: "hidden",
						height: "100%",
						// paddingBottom: "120px",
						display: "flex",
						justifyContent: "center",
						flexDirection: "column",
						alignItems: "center",
					}}
				>
					<Outlet />
				</div>
			</div>
		);
	};

	const router = createHashRouter([
		{
			path: "login",
			element: (
				<RedirectLogedinUser>
					<LoginPage />
				</RedirectLogedinUser>
			),
		},
		{
			path: "/",
			element: (
				<ProtectedRoute>
					<Layout />
				</ProtectedRoute>
			),
			children: [
				{
					path: "/",
					element: <Navigate to="/home" />,
				},
				{
					path: "duesLists",
					element: <Outlet />,
					children: [
						{
							path: "",
							element: <DuesListsPage />,
						},

						{
							path: "FinancialBonusDuesFormPage",
							element: <FinancialBonusDuesFormPage />,
						},
						{
							path: "MedicalCareDuesFormPage",
							element: <MedicalCareDuesFormPage />,
						},
						{
							path: "FinancialBonusDuesFormPage/edit",
							element: <FinancialBonusDuesEditFormPage />,
						},
						{
							path: "print",
							element: <ReportViewer />,
						},

						// {
						// 	path: "addHrDues",
						// 	element: <HrDuesFormPage />,
						// },
						// {
						// 	path: "editHrDues",
						// 	element: <HrDuesFormPage />,
						// },
					],
				},
				{
					path: "search",
					element: <Search />,
				},
				{
					path: "home",
					element: <HomePage />,
				},
				{
					path: "receive",
					element: <Outlet />,
					children: [
						{
							path: "",
							element: <ReceivesPage />,
						},
						{
							path: "add",
							element: <ReceiveFormPage />,
						},
						{
							path: "edit",
							element: <ReceiveEditFormPage />,
						},

						{
							path: "print",
							element: <ReceiveTemplate />,
						},
					],
				},

				{
					path: "pay",
					element: <Outlet />,
					children: [
						{
							path: "",
							element: <PayPage />,
							children: [
								{ path: "paysList", element: <PaysListPage /> },
								{ path: "search", element: <Search /> },
							],
						},
						{
							path: "add",
							element: <AddPayPage />,
						},
						{
							path: "print",
							element: <PayTemplate />,
						},
						{
							path: "printDues",
							element: <PayDuesTemplate />,
						},
						{
							path: "printDuesList",
							element: <PayDuesListTemplate />,
						},
					],
				},
				{
					path: "users",
					element: <Outlet />,
					children: [
						{
							path: "",
							element: <UsersPage />,
						},
						{
							path: "add",
							element: <UserFormPage />,
						},
						{
							path: "edit",
							element: <UserFormPage />,
						},
					],
				},
				{
					path: "beneficiaries",
					element: <Outlet />,
					children: [
						{
							path: "",
							element: <BeneficiariesPage />,
						},
						{
							path: "add",
							element: <BeneficiaryFormPage />,
						},
						{
							path: "edit",
							element: <BeneficiaryFormPage />,
						},
					],
				},
				{
					path: "dueTypes",
					element: <Outlet />,
					children: [
						{
							path: "",
							element: <DueTypesPage />,
						},
						{
							path: "add",
							element: <DueTypesFormPage />,
						},
						{
							path: "edit",
							element: <DueTypesFormPage />,
						},
					],
				},
				{
					path: "ReceiveReport",
					element: <Outlet />,
					children: [
						{
							path: "",
							element: <ReceiveReport />,
						},
						{
							path: "print",
							element: <ReceiveReportTemplate />,
						},
					],
				},
				{
					path: "year",
					element: <SelectYearPage />,
				},
			],
		},
	]);
	return <RouterProvider router={router} />;
}

export default App;
