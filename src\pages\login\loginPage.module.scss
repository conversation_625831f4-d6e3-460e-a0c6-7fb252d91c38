.container {
	width: 100%;
	height: 100vh;

	display: flex;
	align-items: center;
	justify-content: center;
	.innerContainer {
		width: 600px;
		height: 400px;
		display: flex;
		background-color: white;
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
		border: 1px solid rgb(241, 241, 241);
		border-radius: 3px;
		overflow: hidden;
	}
	.imgContainer {
		flex: 1;
		padding: 15px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background: linear-gradient(
			rgba(148, 0, 0, 0.3),
			rgba(255, 255, 255, 0.3),
			rgba(0, 0, 0, 0.3)
		);
		img {
			width: 100%;
			object-fit: contain;
		}
		p {
			margin: 5px 0;
			font-size: 18px;
			font-weight: 700;
		}
	}
	.form {
		padding: 15px;
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 15px;
		justify-content: center;
	}
}
