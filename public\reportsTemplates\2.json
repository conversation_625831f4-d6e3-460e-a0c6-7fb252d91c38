{"id": 45, "title": "11", "date": "2025-08-22", "state": "قابل للدفع", "receive_id": 33, "message_sent": 0, "type": "غذاءات", "dues": [{"id": 9, "beneficiary_id": 113, "month": 4, "days": 2, "year": 2025, "dues_list_id": 45, "amount": "6000.00", "net_amount": "5100.00", "tax": "900.00", "state": "قابل للدفع", "added_at": "2025-08-22", "paid_at": null, "message_sent": 0, "pay_id": null, "user_id": null, "beneficiary.name": "<PERSON><PERSON><PERSON><PERSON>", "beneficiary.job_title.id": 6, "beneficiary.job_title.title": "موظف", "beneficiary.job_title.feeding_amount": "3000.00", "beneficiary.job_type.id": 1, "beneficiary.job_type.name": "إداري", "beneficiary.job_type.max_feeding_days": 22, "beneficiary.job_type.feeding_tax": 15, "beneficiary.job_degree.id": 1, "beneficiary.job_degree.degree": 1}, {"id": 10, "beneficiary_id": 114, "month": 4, "days": 2, "year": 2025, "dues_list_id": 45, "amount": "6000.00", "net_amount": "6000.00", "tax": "0.00", "state": "قابل للدفع", "added_at": "2025-08-22", "paid_at": null, "message_sent": 0, "pay_id": null, "user_id": null, "beneficiary.name": "ا<PERSON><PERSON><PERSON>", "beneficiary.job_title.id": 6, "beneficiary.job_title.title": "موظف", "beneficiary.job_title.feeding_amount": "3000.00", "beneficiary.job_type.id": 2, "beneficiary.job_type.name": "فني", "beneficiary.job_type.max_feeding_days": 26, "beneficiary.job_type.feeding_tax": 0, "beneficiary.job_degree.id": 1, "beneficiary.job_degree.degree": 1}]}