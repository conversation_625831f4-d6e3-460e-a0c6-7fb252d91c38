import React, { useEffect, useState } from "react";
import { useMutation, useQuery } from "react-query";
import { useLocation } from "react-router-dom";
import { toast } from "react-toastify";
import useNavigateWithQuery from "../../../hooks/useNavigateWithQuery";

import TopBar from "../../../components/TopBar/TopBar";
import Row from "../../../UI/row/Row";

import {
	<PERSON>ton,
	Card,
	CardHeader,
	CardBody,
	Table,
	TableHeader,
	TableColumn,
	TableBody,
	TableRow,
	TableCell,
	Input,
	Select,
	SelectItem,
	Tooltip,
	Autocomplete,
	AutocompleteItem,
	Checkbox,
	CheckboxGroup,
} from "@heroui/react";

import { X, Save, Trash } from "@mynaui/icons-react";

import {
	getAllBeneficiaries,
	getAllClauses,
	getDuesListById,
	updateDuesList,
} from "../../../api/serverApi";

const FinancialBonusDuesEditFormPage = () => {
	// hooks
	const navigate = useNavigateWithQuery();
	const info = useLocation();
	// state
	const [title, setTitle] = useState("");
	const [date, setDate] = useState(new Date().toISOString().split("T")[0]);
	const [beneficiaries, setBeneficiaries] = useState([]);
	const [addedEmployees, setAddedEmployees] = useState([]);
	const [employeesCount, setEmployeesCount] = useState(0);
	const [addedNonEployees, setAddedNonEmployees] = useState([]);
	const [nonEmployeesCount, setNonEmployeesCount] = useState(0);
	const [selectedTypes, setSelectedTypes] = useState([]);
	const [selectedDiscounts, setSelectedDiscounts] = useState([]);
	const [amount, setAmount] = useState(0);
	const [tax, setTax] = useState(0);
	const [taxValue, setTaxValue] = useState(0);
	const [deductionValue, setDeductionValue] = useState(0);
	const [deduction, setDeduction] = useState(0);
	const [clause, setClause] = useState("");
	const [netAmount, setNetAmount] = useState(0);
	// queries
	useQuery({
		queryKey: ["beneficiaries"],
		queryFn: getAllBeneficiaries,
		select: (res) => res.data.beneficiaries.map((el) => ({ ...el })),
		onSuccess: (res) => {
			setBeneficiaries(res);
		},
	});
	const { data: clauses } = useQuery({
		queryKey: ["clauses"],
		queryFn: getAllClauses,
		select: (res) => res.data.clauses,
	});
	const { data: duesListData } = useQuery({
		queryKey: ["duesListData", info.state?.id],
		queryFn: getDuesListById,
		select: (res) => {
			return res.data.duesList;
		},
		onSuccess: (data) => {
			let employees = [];
			let nonEmployees = [];
			let types = [];
			let discounts = [];
			data.dues.forEach((el, i) => {
				if (el.beneficiary_id) {
					employees.push({
						id: i + 1,
						db_id: el.id,
						beneficiary_id: `${el.beneficiary_id}`,
						amount: +el.amount,
						deduction: +el.deduction,
						deductionValue: (+el.deduction / +el.amount) * 100,
						tax: +el.tax,
						taxValue: (+el.tax / (+el.amount - +el.deduction)) * 100,
						net_amount: +el.net_amount,
						name: el.beneficiary.name,
					});
				} else {
					nonEmployees.push({
						id: i + 1,
						db_id: el.id,
						amount: +el.amount,
						deduction: +el.deduction,
						deductionValue: (+el.deduction / +el.amount) * 100,
						tax: +el.tax,
						taxValue: (+el.tax / (+el.amount - +el.deduction)) * 100,
						net_amount: +el.net_amount,
						name: el.name,
					});
				}
				if (el.deduction > 0 && !discounts.includes("خصم حسب التوجيهات")) {
					discounts.push("خصم حسب التوجيهات");
				}
				if (el.tax > 0 && !discounts.includes("ضريبة")) {
					discounts.push("ضريبة");
				}
			});
			if (employees.length > 0) {
				types.push("موظفين");
			}
			if (nonEmployees.length > 0) {
				types.push("غير موظفين");
			}

			setAddedEmployees(employees);
			setEmployeesCount(employees.length);
			setAddedNonEmployees(nonEmployees);
			setNonEmployeesCount(nonEmployees.length);
			setSelectedTypes(types);
			setSelectedDiscounts(discounts);
			setTitle(data.title);
			setDate(data.date);
			setClause(`${data.clause_id}`);
		},
		enabled: !!info.state,
	});
	// mutations
	const updateMutation = useMutation({
		mutationFn: updateDuesList,
		onSuccess: () => {
			toast.success("تم تعديل كشف المستحقات بنجاح", {
				position: toast.POSITION.TOP_CENTER,
			});
			navigate("./../..");
		},
		onError: (err) => {
			toast.error(err.response?.data?.message || "حدث خطأ", {
				position: toast.POSITION.TOP_CENTER,
			});
		},
	});

	// effects
	useEffect(() => {
		let total = 0;
		let tax = 0;
		let deduction = 0;
		let net_amount = 0;
		const updatedBeneficiaries = beneficiaries.map((beneficiary) => {
			const match = addedEmployees.find(
				(employee) => +employee.beneficiary_id === +beneficiary.id
			);
			if (match) return { ...beneficiary, disabled: true };
			return { ...beneficiary, disabled: false };
		});
		addedEmployees.forEach((el) => {
			total = total + +el.amount;
			el.deduction = (el.deductionValue * el.amount) / 100;
			el.tax = (el.taxValue * (el.amount - el.deduction)) / 100;
			el.net_amount = el.amount - el.tax - el.deduction;
			tax = tax + +el.tax;
			deduction = deduction + +el.deduction;
			net_amount = net_amount + +el.net_amount;
		});
		addedNonEployees.forEach((el) => {
			total = total + +el.amount;
			el.deduction = (el.deductionValue * el.amount) / 100;
			el.tax = (el.taxValue * (el.amount - el.deduction)) / 100;
			el.net_amount = el.amount - el.tax - el.deduction;
			tax = tax + +el.tax;
			deduction = deduction + +el.deduction;
			net_amount = net_amount + +el.net_amount;
		});
		setAmount(total);
		setTax(tax);
		setDeduction(deduction);
		setNetAmount(net_amount);
		setBeneficiaries(updatedBeneficiaries);
	}, [addedEmployees, addedNonEployees]);

	// useEffect(() => {
	// 	if (addedEmployees.length > 0) {
	// 		const updated = addedEmployees.map((el) => {
	// 			const tax = (+el.amount * el.taxValue) / 100 || 0;
	// 			const net_amount = +el.amount - (+el.amount * el.taxValue) / 100 || 0;
	// 			return {
	// 				...el,
	// 				tax: tax,
	// 				net_amount,
	// 			};
	// 		});
	// 		setAddedEmployees(updated);
	// 	}
	// }, [taxValue]);

	// handlers
	// handlers
	const titleChangeHandler = (e) => setTitle(e.target.value);
	const employeeChangeHandler = (beneficiary_id, fieldId) => {
		const updated = addedEmployees.map((el) => {
			if (el.id === fieldId) {
				return {
					...el,
					beneficiary_id: beneficiary_id,
					amount: 0,
					name: beneficiaries.find((el) => el.id === +beneficiary_id).name,
					tax: 0,
					taxValue: 0,
					deduction: 0,
					deductionValue: 0,
					net_amount: 0,
				};
			}
			return el;
		});
		setAddedEmployees(updated);
	};
	const employeeAmountChangeHandler = (e, employee) => {
		const updated = addedEmployees.map((el) => {
			if (el.id === employee.id) {
				const tax = (+e.target.value * el.taxValue) / 100 || 0;
				const net_amount =
					+e.target.value - (+e.target.value * el.taxValue) / 100 || 0;
				return {
					...el,
					tax: tax,
					net_amount,
					amount: +e.target.value,
				};
			}
			return el;
		});
		setAddedEmployees(updated);
	};
	const nonEmployeeAmountChangeHandler = (e, employee) => {
		const updated = addedNonEployees.map((el) => {
			if (el.id === employee.id) {
				const tax = (+e.target.value * el.taxValue) / 100 || 0;
				const net_amount =
					+e.target.value - (+e.target.value * el.taxValue) / 100 || 0;
				return {
					...el,
					tax: tax,
					net_amount,
					amount: +e.target.value,
				};
			}
			return el;
		});
		setAddedNonEmployees(updated);
	};
	const employeeTaxChangeHandler = (e, employee) => {
		const updated = addedEmployees.map((el) => {
			if (el.id === employee.id) {
				return {
					...el,
					taxValue: +e.target.value,
				};
			}
			return el;
		});
		setAddedEmployees(updated);
	};
	const employeeDeductionChangeHandler = (e, employee) => {
		const updated = addedEmployees.map((el) => {
			if (el.id === employee.id) {
				return {
					...el,
					deductionValue: +e.target.value,
				};
			}
			return el;
		});
		console.log(`updated`, updated);
		setAddedEmployees(updated);
	};
	const nonEmployeeTaxChangeHandler = (e, employee) => {
		const updated = addedNonEployees.map((el) => {
			if (el.id === employee.id) {
				return {
					...el,
					taxValue: +e.target.value,
				};
			}
			return el;
		});
		setAddedNonEmployees(updated);
	};
	const nonEmployeeDeductionChangeHandler = (e, employee) => {
		const updated = addedNonEployees.map((el) => {
			if (el.id === employee.id) {
				return {
					...el,
					deductionValue: +e.target.value,
				};
			}
			return el;
		});
		setAddedNonEmployees(updated);
	};
	const nonEmployeeNameChangeHandler = (e, employee) => {
		const updated = addedNonEployees.map((el) => {
			if (el.id === employee.id) {
				return {
					...el,
					name: e.target.value,
				};
			}
			return el;
		});
		setAddedNonEmployees(updated);
	};
	const addEmployeeChangeHandler = () => {
		setAddedEmployees((prev) => [
			...prev,
			{
				id: employeesCount + 1,
				name: "",
				amount: 0,
				tax: 0,
				taxValue: 0,
				deduction: 0,
				deductionValue: 0,
				net_amount: 0,
				beneficiary_id: null,
			},
		]);
		setEmployeesCount((prev) => prev + 1);
	};
	const addNonEmployeeChangeHandler = () => {
		setAddedNonEmployees((prev) => [
			...prev,
			{
				id: nonEmployeesCount + 1,
				name: "",
				amount: 0,
				tax: 0,
				taxValue: 0,
				deduction: 0,
				deductionValue: 0,
				net_amount: 0,
			},
		]);
		setNonEmployeesCount((prev) => prev + 1);
	};
	const onSaveHandler = (e) => {
		e.preventDefault();
		if (
			addedEmployees.filter((el) => el.amount === 0).length > 0 ||
			addedNonEployees.filter((el) => el.amount === 0).length > 0
		) {
			toast.error("الرجاء اضافة مبلغ صحيح", {
				position: toast.POSITION.TOP_CENTER,
			});
			return;
		}
		if (addedEmployees.length === 0 && addedNonEployees.length === 0) {
			toast.error("الرجاء اضافة مستفيد واحد  على الاقل", {
				position: toast.POSITION.TOP_CENTER,
			});
			return;
		}
		const payload = {
			title: title.replace(/\s+/g, " ").trim(),
			added: [...addedEmployees, ...addedNonEployees],
			date,
			clause,
			type: "مكافآت",
		};
		updateMutation.mutate({ ...payload, id: info.state.id });
	};
	// table columns
	return (
		<div className="w-full h-full overflow-auto">
			<form onSubmit={onSaveHandler}>
				<TopBar
					right={
						<div className="flex gap-3">
							<Button
								variant="flat"
								color="warning"
								onPress={() => {
									navigate("./../..");
								}}
								startContent={<X />}
							>
								الغاء
							</Button>
							<Button
								color="primary"
								type="submit"
								isDisabled={updateDuesList.isLoading}
								startContent={<Save />}
							>
								حفظ
							</Button>
						</div>
					}
				/>
				<div className="w-full p-5 pb-16">
					<Card>
						<CardHeader className="bg-primary text-default-50 font-bold text-medium">
							بيانات المستند
						</CardHeader>
						<CardBody>
							<Row flex={[2, 1, 1]}>
								<Input
									label="العنوان"
									value={title}
									onChange={titleChangeHandler}
									placeholder="اكتب العنوان"
									size="sm"
									isRequired
								/>
								<Input
									label="تاريخ الاضافة"
									value={date}
									type="date"
									onChange={(e) => setDate(e.target.value)}
									size="sm"
									isRequired
								/>
								<Select
									selectedKeys={clause}
									label="البند"
									size="sm"
									onChange={(e) => setClause(e.target.value)}
								>
									{clauses &&
										clauses.map((el) => (
											<SelectItem className="text-right" key={el.id}>
												{el.name}
											</SelectItem>
										))}
								</Select>
							</Row>
							<Row flex={[1, 2, 1]}>
								<CheckboxGroup
									label="نوع المستفيدين"
									orientation="horizontal"
									className="text-right"
									value={selectedTypes}
									onValueChange={setSelectedTypes}
									onChange={(e) => {
										if (!e.includes("موظفين")) {
											setEmployeesCount(0);
											setAddedEmployees([]);
										}
										if (!e.includes("غير موظفين")) {
											setNonEmployeesCount(0);
											setAddedNonEmployees([]);
										}
									}}
								>
									<Checkbox value="موظفين">موظفين</Checkbox>
									<Checkbox value="غير موظفين">غير موظفين</Checkbox>
								</CheckboxGroup>
								<CheckboxGroup
									label="نوع الخصم"
									orientation="horizontal"
									className="text-right"
									value={selectedDiscounts}
									onValueChange={setSelectedDiscounts}
									onChange={(e) => {
										if (!e.includes("خصم حسب التوجيهات")) {
											const updatedAddedEmployees = addedEmployees.map(
												(el) => ({
													...el,
													deductionValue: 0,
													deduction: 0,
												})
											);
											const updatedAddedNonEmployees = addedNonEployees.map(
												(el) => ({
													...el,
													deductionValue: 0,
													deduction: 0,
												})
											);
											setAddedEmployees(updatedAddedEmployees);
											setAddedNonEmployees(updatedAddedNonEmployees);
										}
										if (!e.includes("ضريبة")) {
											const updatedAddedEmployees = addedEmployees.map(
												(el) => ({
													...el,
													taxValue: 0,
													tax: 0,
												})
											);
											const updatedAddedNonEmployees = addedNonEployees.map(
												(el) => ({
													...el,
													taxValue: 0,
													tax: 0,
												})
											);
											setAddedEmployees(updatedAddedEmployees);
											setAddedNonEmployees(updatedAddedNonEmployees);
										}
									}}
								>
									<Checkbox value="خصم حسب التوجيهات">
										خصم حسب التوجيهات
									</Checkbox>
									<Checkbox value="ضريبة">ضريبة</Checkbox>
								</CheckboxGroup>
								<div className="flex  gap-3">
									{selectedDiscounts.includes("خصم حسب التوجيهات") && (
										<Input
											label="خصم حسب التوجيهات (%)"
											value={deductionValue}
											onChange={(e) => {
												setDeductionValue(e.target.value);
												const updatedEmployees = addedEmployees.map((el) => ({
													...el,
													deductionValue: e.target.value,
												}));
												const updatedNonEmployees = addedNonEployees.map(
													(el) => ({
														...el,
														deductionValue: e.target.value,
													})
												);
												setAddedEmployees(updatedEmployees);
												setAddedNonEmployees(updatedNonEmployees);
											}}
											type="number"
											size="sm"
										/>
									)}
									{selectedDiscounts.includes("ضريبة") && (
										<Select
											selectedKeys={taxValue}
											label="الضريبة (%)"
											size="sm"
											onChange={(e) => {
												setTaxValue(e.target.value);
												const updatedEmployees = addedEmployees.map((el) => ({
													...el,
													taxValue: e.target.value,
												}));
												const updatedNonEmployees = addedNonEployees.map(
													(el) => ({
														...el,
														taxValue: e.target.value,
													})
												);
												setAddedEmployees(updatedEmployees);
												setAddedNonEmployees(updatedNonEmployees);
											}}
										>
											<SelectItem key={2}>2%</SelectItem>
											<SelectItem key={7}>7%</SelectItem>
											<SelectItem key={15}>15%</SelectItem>
										</Select>
									)}
								</div>
							</Row>
						</CardBody>
					</Card>
					{beneficiaries && selectedTypes.includes("موظفين") ? (
						<Card>
							<CardHeader className="bg-primary text-default-50 font-bold text-medium">
								بيانات الموظفين
							</CardHeader>
							<CardBody>
								<Table
									aria-labelledby="employees-table"
									className="min-w-[510px]"
								>
									<TableHeader>
										<TableColumn>الاسم</TableColumn>
										<TableColumn>المبلغ</TableColumn>
										<TableColumn
											hidden={!selectedDiscounts.includes("خصم حسب التوجيهات")}
										>
											نسبة الخصم(%)
										</TableColumn>
										<TableColumn
											hidden={!selectedDiscounts.includes("خصم حسب التوجيهات")}
										>
											الخصم
										</TableColumn>
										<TableColumn hidden={!selectedDiscounts.includes("ضريبة")}>
											نسبة الضريبة(%)
										</TableColumn>
										<TableColumn hidden={!selectedDiscounts.includes("ضريبة")}>
											الضريبة
										</TableColumn>
										<TableColumn>صافي المبلغ</TableColumn>
										<TableColumn>خيارات</TableColumn>
									</TableHeader>
									<TableBody>
										{addedEmployees.map((item) => {
											const currentRow = addedEmployees.find(
												(el) => el.id === item.id
											);
											return (
												<TableRow key={item.id}>
													<TableCell>
														<Autocomplete
															className="max-w-xs"
															label="اسم"
															onSelectionChange={(beneficiary_id) => {
																employeeChangeHandler(beneficiary_id, item.id);
															}}
															isClearable={false}
															selectedKey={currentRow?.beneficiary_id ?? null}
														>
															{beneficiaries.map((el) => (
																<AutocompleteItem
																	key={el.id}
																	dir="rtl"
																	className="text-right"
																	isDisabled={el.disabled}
																>
																	{el.name}
																</AutocompleteItem>
															))}
														</Autocomplete>
													</TableCell>
													<TableCell>
														<Input
															value={currentRow.amount}
															type="number"
															size="sm"
															min={0}
															onChange={(event) => {
																employeeAmountChangeHandler(event, item);
															}}
															isRequired
														/>
													</TableCell>
													<TableCell
														hidden={
															!selectedDiscounts.includes("خصم حسب التوجيهات")
														}
													>
														<Input
															value={currentRow.deductionValue}
															type="number"
															size="sm"
															onChange={(event) => {
																employeeDeductionChangeHandler(event, item);
															}}
															isRequired
														/>
													</TableCell>
													<TableCell
														hidden={
															!selectedDiscounts.includes("خصم حسب التوجيهات")
														}
													>
														<Input
															value={currentRow.deduction}
															type="number"
															size="sm"
															isDisabled
															isRequired
														/>
													</TableCell>
													<TableCell
														hidden={!selectedDiscounts.includes("ضريبة")}
													>
														<Input
															value={currentRow.taxValue}
															type="number"
															size="sm"
															onChange={(event) => {
																employeeTaxChangeHandler(event, item);
															}}
															isRequired
														/>
													</TableCell>
													<TableCell
														hidden={!selectedDiscounts.includes("ضريبة")}
													>
														<Input
															value={currentRow.tax}
															type="number"
															size="sm"
															isDisabled
															isRequired
														/>
													</TableCell>
													<TableCell>
														<Input
															value={currentRow.net_amount}
															type="number"
															size="sm"
															isDisabled
															isRequired
														/>
													</TableCell>
													<TableCell>
														<Tooltip content="حذف" placement="top">
															<Button
																isIconOnly
																variant="flat"
																className="bg-danger text-white"
																onPress={() =>
																	setAddedEmployees((prev) =>
																		prev.filter((el) => el.id !== item.id)
																	)
																}
															>
																<Trash />
															</Button>
														</Tooltip>
													</TableCell>
												</TableRow>
											);
										})}
									</TableBody>
								</Table>
								<Row>
									<Button color="primary" onPress={addEmployeeChangeHandler}>
										إضافة
									</Button>
								</Row>
							</CardBody>
						</Card>
					) : null}
					{selectedTypes.includes("غير موظفين") ? (
						<Card>
							<CardHeader className="bg-primary text-default-50 font-bold text-medium">
								بيانات غير الموظفين
							</CardHeader>
							<CardBody>
								<Table
									aria-labelledby="employees-table"
									className="min-w-[510px]"
								>
									<TableHeader>
										<TableColumn>الاسم</TableColumn>
										<TableColumn>المبلغ</TableColumn>
										<TableColumn
											hidden={!selectedDiscounts.includes("خصم حسب التوجيهات")}
										>
											نسبة الخصم(%)
										</TableColumn>
										<TableColumn
											hidden={!selectedDiscounts.includes("خصم حسب التوجيهات")}
										>
											الخصم
										</TableColumn>
										<TableColumn hidden={!selectedDiscounts.includes("ضريبة")}>
											نسبة الضريبة(%)
										</TableColumn>
										<TableColumn hidden={!selectedDiscounts.includes("ضريبة")}>
											الضريبة
										</TableColumn>
										<TableColumn>صافي المبلغ</TableColumn>
										<TableColumn>خيارات</TableColumn>
									</TableHeader>
									<TableBody>
										{addedNonEployees.map((item) => {
											const currentRow = addedNonEployees.find(
												(el) => el.id === item.id
											);
											return (
												<TableRow key={item.id}>
													<TableCell>
														<Input
															value={currentRow.name}
															onChange={(event) => {
																nonEmployeeNameChangeHandler(event, item);
															}}
															isRequired
														/>
													</TableCell>
													<TableCell>
														<Input
															value={currentRow.amount}
															type="number"
															size="sm"
															min={0}
															onChange={(event) => {
																nonEmployeeAmountChangeHandler(event, item);
															}}
															isRequired
														/>
													</TableCell>
													<TableCell
														hidden={
															!selectedDiscounts.includes("خصم حسب التوجيهات")
														}
													>
														<Input
															value={currentRow.deductionValue}
															type="number"
															size="sm"
															onChange={(event) => {
																nonEmployeeDeductionChangeHandler(event, item);
															}}
															isRequired
														/>
													</TableCell>
													<TableCell
														hidden={
															!selectedDiscounts.includes("خصم حسب التوجيهات")
														}
													>
														<Input
															value={currentRow.deduction}
															type="number"
															size="sm"
															isDisabled
															isRequired
														/>
													</TableCell>
													<TableCell
														hidden={!selectedDiscounts.includes("ضريبة")}
													>
														<Input
															value={currentRow.taxValue}
															type="number"
															size="sm"
															onChange={(event) => {
																nonEmployeeTaxChangeHandler(event, item);
															}}
															isRequired
														/>
													</TableCell>
													<TableCell
														hidden={!selectedDiscounts.includes("ضريبة")}
													>
														<Input
															value={currentRow.tax}
															type="number"
															size="sm"
															isDisabled
															isRequired
														/>
													</TableCell>
													<TableCell>
														<Input
															value={currentRow.net_amount}
															type="number"
															size="sm"
															isDisabled
															isRequired
														/>
													</TableCell>
													<TableCell>
														<Tooltip content="حذف" placement="top">
															<Button
																isIconOnly
																variant="flat"
																className="bg-danger text-white"
																onPress={() =>
																	setAddedEmployees((prev) =>
																		prev.filter((el) => el.id !== item.id)
																	)
																}
															>
																<Trash />
															</Button>
														</Tooltip>
													</TableCell>
												</TableRow>
											);
										})}
									</TableBody>
								</Table>
								<Row>
									<Button color="primary" onPress={addNonEmployeeChangeHandler}>
										إضافة
									</Button>
								</Row>
							</CardBody>
						</Card>
					) : null}
					{selectedTypes.length > 0 ? (
						<Card>
							<CardHeader className="bg-primary text-default-50 font-bold text-medium">
								الاجمالي
							</CardHeader>
							<CardBody>
								<Table
									aria-labelledby="employees-table"
									className="min-w-[510px]"
								>
									<TableHeader>
										<TableColumn></TableColumn>
										<TableColumn>المبلغ</TableColumn>
										{/* <TableColumn
												hidden={!selectedDiscounts.includes("خصم حسب التوجيهات")}
											>
												نسبة الخصم(%)
											</TableColumn> */}
										<TableColumn
											hidden={!selectedDiscounts.includes("خصم حسب التوجيهات")}
										>
											الخصم
										</TableColumn>
										{/* <TableColumn hidden={!selectedDiscounts.includes("ضريبة")}>
												نسبة الضريبة(%)
											</TableColumn> */}
										<TableColumn hidden={!selectedDiscounts.includes("ضريبة")}>
											الضريبة
										</TableColumn>
										<TableColumn>صافي المبلغ</TableColumn>
									</TableHeader>
									<TableBody>
										<TableRow>
											<TableCell className=" font-bold text-large">
												الاجمالي
											</TableCell>
											<TableCell className=" font-bold text-large">
												{amount}
											</TableCell>
											{/* <TableCell
													hidden={
														!selectedDiscounts.includes("خصم حسب التوجيهات")
													}
													className=" font-bold text-large"
												></TableCell> */}
											<TableCell
												hidden={
													!selectedDiscounts.includes("خصم حسب التوجيهات")
												}
												className=" font-bold text-large"
											>
												{deduction}
											</TableCell>
											{/* <TableCell
													hidden={!selectedDiscounts.includes("ضريبة")}
													className=" font-bold text-large"
												></TableCell> */}
											<TableCell
												hidden={!selectedDiscounts.includes("ضريبة")}
												className=" font-bold text-large"
											>
												{tax}
											</TableCell>
											<TableCell className=" font-bold text-large">
												{netAmount}
											</TableCell>
										</TableRow>
									</TableBody>
								</Table>
							</CardBody>
						</Card>
					) : null}
				</div>
			</form>
		</div>
	);
};

export default FinancialBonusDuesEditFormPage;
