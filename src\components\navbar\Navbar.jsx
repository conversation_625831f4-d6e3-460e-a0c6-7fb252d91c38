import { Link } from "react-router-dom";
import { useContext, useState } from "react";
import { AuthContext } from "../../store/auth-context";

import {
	Menubar,
	MenubarContent,
	MenubarItem,
	MenubarMenu,
	MenubarTrigger,
} from "@/components/ui/menubar";
import {
	Modal,
	ModalContent,
	ModalHeader,
	ModalBody,
	ModalFooter,
	useDisclosure,
	Card,
	CardBody,
	Button,
	Select,
	SelectItem,
} from "@heroui/react";
import {
	Home,
	ArrowUpDown,
	FileText,
	Cog,
	Logout,
	Check,
	X,
} from "@mynaui/icons-react";
// path may vary

const Navbar = () => {
	//hooks
	const [year, setYear] = useState("");
	const authCtx = useContext(AuthContext);
	console.log(`authCtx`, authCtx.permissions.addPay);
	const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure();
	//states
	const [dialogMode, setDialogMode] = useState("");
	const logoutHandler = async () => {
		try {
			authCtx.logout();
		} catch (err) {
			console.log(`err`, err);
		}
	};
	return (
		<div className="flex items-center pl-5">
			<Modal isOpen={isOpen} onOpenChange={onOpenChange} size="xl">
				<ModalContent>
					{() => (
						<>
							<ModalHeader className="flex flex-col gap-1">
								{dialogMode === "changeYear" && "الاتصال بسنوات سابقة"}
							</ModalHeader>
							<ModalBody>
								{dialogMode === "changeYear" && (
									<Select
										label="الاتصال بسنوات سابقة"
										selectedKeys={year ? [year] : []}
										onSelectionChange={(keys) => {
											const val = Array.from(keys)[0] || "";
											setYear(val);
										}}
									>
										<SelectItem key={2023}>2023</SelectItem>
										<SelectItem key={2024}>2024</SelectItem>
										<SelectItem key={2025}>2025</SelectItem>
									</Select>
								)}
							</ModalBody>
							<ModalFooter>
								{dialogMode === "changeYear" && (
									<div className="flex gap-3">
										<Button
											variant="flat"
											onPress={() => {
												onClose();
											}}
											color="danger"
											startContent={<X />}
										>
											الغاء
										</Button>
										{dialogMode === "changeYear" && (
											<Button
												color="primary"
												onPress={() => {
													authCtx.setYear(year);
													onClose();
												}}
												startContent={<Check />}
											>
												تأكيد
											</Button>
										)}
									</div>
								)}
							</ModalFooter>
						</>
					)}
				</ModalContent>
			</Modal>
			<Menubar className="text-right flex-1">
				<MenubarMenu>
					<Link to="/home">
						<MenubarTrigger className="cursor-pointer">
							<Home />
							الرئيسية
						</MenubarTrigger>
					</Link>
				</MenubarMenu>
				<MenubarMenu>
					<Link to="/duesLists">
						<MenubarTrigger className="cursor-pointer">
							<Home />
							المستحقات
						</MenubarTrigger>
					</Link>
				</MenubarMenu>
				{authCtx.permissions.addPay ||
				authCtx.permissions.deletePay ||
				authCtx.permissions.addReceive ||
				authCtx.permissions.deleteReceive ? (
					<MenubarMenu>
						<MenubarTrigger className="cursor-pointer">
							<ArrowUpDown />
							الصندوف
						</MenubarTrigger>
						<MenubarContent>
							{authCtx.permissions.addReceive ||
								(authCtx.permissions.deleteReceive ? (
									<Link to="/receive">
										<MenubarItem className="justify-end cursor-pointer">
											القبض
										</MenubarItem>
									</Link>
								) : null)}
							{authCtx.permissions.addPay ||
								(authCtx.permissions.deletePay ? (
									<Link to="/pay/search">
										<MenubarItem className="justify-end cursor-pointer">
											الصرف
										</MenubarItem>
									</Link>
								) : null)}
						</MenubarContent>
					</MenubarMenu>
				) : null}

				<MenubarMenu>
					<MenubarTrigger className="cursor-pointer">
						<FileText />
						التقارير
					</MenubarTrigger>
					<MenubarContent>
						<Link to="/ReceiveReport">
							<MenubarItem className="justify-end">تقارير الاستلام</MenubarItem>
						</Link>
					</MenubarContent>
				</MenubarMenu>
				<MenubarMenu>
					<MenubarTrigger className="cursor-pointer">
						<Cog />
						الاعدادات
					</MenubarTrigger>
					<MenubarContent>
						<Link to="/users">
							<MenubarItem className="justify-end cursor-pointer">
								المستخدمين
							</MenubarItem>
						</Link>
						<Link to="/beneficiaries">
							<MenubarItem className="justify-end cursor-pointer">
								المستفيدين
							</MenubarItem>
						</Link>
						<Link to="/dueTypes">
							<MenubarItem className="justify-end cursor-pointer">
								انواع الاستحقاق
							</MenubarItem>
						</Link>
						<Link
							onClick={() => {
								setDialogMode("changeYear");
								onOpen();
							}}
						>
							<MenubarItem className="justify-end cursor-pointer">
								الاتصال بسنوات سابقة
							</MenubarItem>
						</Link>
					</MenubarContent>
				</MenubarMenu>
			</Menubar>
			{authCtx.year && (
				<Card>
					<CardBody className=" font-bold">
						<p>{authCtx.year}</p>
					</CardBody>
				</Card>
			)}

			<button
				onClick={logoutHandler}
				className="flex mr-auto pr-auto font-bold my-1 gap-1 bg-danger-700 p-1 px-3 rounded-sm text-white"
			>
				<Logout />
				تسجيل خروج
			</button>
		</div>
	);
};

export default Navbar;
