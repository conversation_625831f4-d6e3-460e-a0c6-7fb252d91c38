import { But<PERSON>, Field, Input, Select } from "@fluentui/react-components";
import Row from "../../UI/row/Row";
import Card from "../../UI/card/Card";
import { DismissRegular, SaveRegular } from "@fluentui/react-icons";
import { useLocation, useNavigate } from "react-router-dom";
import { useState } from "react";
import { useMutation, useQuery } from "react-query";
import {
	addBeneficiary,
	ediBeneficiary,
	getAllJobDegrees,
	getAllJobTitles,
	getAllJobTypes,
	getBeneficiary,
} from "../../api/serverApi";
import { toast } from "react-toastify";
import TopBar from "../../components/TopBar/TopBar";
const BeneficiaryFormPage = () => {
	//hooks
	const navigate = useNavigate();
	const info = useLocation();
	//states
	const [name, setName] = useState("");
	const [jobTitle, setJobTitle] = useState("");
	const [jobType, setJobType] = useState("");
	const [jobDegree, setJobDegree] = useState("");

	const [phoneNumber, setPhoneNumber] = useState("");
	const [location, setLocation] = useState("");
	//queries
	useQuery({
		queryKey: ["Beneficiary", info.state?.id],
		queryFn: getBeneficiary,
		onSuccess: (res) => {
			setName(res.data.beneficiary.name);
			setJobTitle(res.data.beneficiary.job_title);
			setJobType(res.data.beneficiary.job_type);
			setPhoneNumber(res.data.beneficiary.phone_number);
			setLocation(res.data.beneficiary.location);
		},
		enabled: !!info.state,
	});
	const { data: jobTitles } = useQuery({
		queryKey: ["jobTtiles"],
		queryFn: getAllJobTitles,
		select: (res) => res.data.titles,
	});
	const { data: jobTypes } = useQuery({
		queryKey: ["jobTypes"],
		queryFn: getAllJobTypes,
		select: (res) => res.data.types,
	});
	const { data: jobDegrees } = useQuery({
		queryKey: ["jobDegrees"],
		queryFn: getAllJobDegrees,
		select: (res) => res.data.degrees,
	});
	const addMutation = useMutation({
		mutationFn: addBeneficiary,
		onSuccess: (res) => {
			toast.success("تم إضافة المستفيد بنجاح", {
				position: toast.POSITION.TOP_CENTER,
			});
			navigate("./..");
		},
		onError: (err) => {
			toast.error(err.response.data.message, {
				position: toast.POSITION.TOP_CENTER,
			});
		},
	});
	const editMutation = useMutation({
		mutationFn: ediBeneficiary,
		onSuccess: (res) => {
			toast.success("تم تعديل المستفيد بنجاح", {
				position: toast.POSITION.TOP_CENTER,
			});
			navigate("./..", {});
		},
		onError: (err) => {
			toast.error(err.response.data.message, {
				position: toast.POSITION.TOP_CENTER,
			});
		},
	});
	//functions
	const nameChangeHandler = (e) => {
		setName(e.target.value);
	};
	const phoneNumberChangeHandler = (e) => {
		setPhoneNumber(e.target.value);
	};
	const locationChangeHandler = (e) => {
		setLocation(e.target.value);
	};

	return (
		<form
			onSubmit={(e) => {
				e.preventDefault();

				{
					info.state
						? editMutation.mutate({
								id: info.state.id,
								name: name.replace(/\s+/g, " ").trim(),
								jobTitle,
								jobType,
								phoneNumber: phoneNumber ? phoneNumber : null,
								location,
						  })
						: addMutation.mutate({
								name: name.replace(/\s+/g, " ").trim(),
								jobTitle,
								jobType,
								jobDegree,
								phoneNumber: phoneNumber ? phoneNumber : null,
								location,
						  });
				}
			}}
		>
			<TopBar
				right={
					<>
						<Button
							appearance="secondary"
							icon={<DismissRegular />}
							onClick={() => {
								navigate("./..");
							}}
						>
							الغاء
						</Button>
						<Button
							appearance="primary"
							icon={<SaveRegular />}
							type="submit"
							disabled={addMutation.isLoading || editMutation.isLoading}
						>
							حفظ
						</Button>
					</>
				}
			/>
			<div
				style={{
					padding: "0 5px",
					marginTop: "80px",
					paddingBottom: "120px",
					display: "flex",
					flexDirection: "column",
					gap: "15px",
				}}
			>
				<Card title="بيانات المستفيد">
					<Row flex={[2, 1, 1]}>
						<Field label="اسم المستفيد" required>
							<Input value={name} onChange={nameChangeHandler} required />
						</Field>
						{jobTitles && (
							<Field label="المسمى الوظيفي">
								<Select
									value={jobTitle}
									onChange={(e) => setJobTitle(e.target.value)}
								>
									{jobTitles &&
										jobTitles.map((el) => (
											<option value={el.id} key={el.id}>
												{el.title}
											</option>
										))}
								</Select>
							</Field>
						)}
						{jobTypes && (
							<Field label="النوع">
								<Select
									value={jobType}
									onChange={(e) => setJobType(e.target.value)}
								>
									<option></option>
									{jobTypes &&
										jobTypes.map((el) => (
											<option value={el.id} key={el.id}>
												{el.name}
											</option>
										))}
								</Select>
							</Field>
						)}
						{jobDegrees && (
							<Field label="الدرجة الوظيفية" required>
								<Select
									value={jobDegree}
									onChange={(e) => setJobDegree(e.target.value)}
								>
									<option></option>
									{jobDegrees &&
										jobDegrees.map((el) => (
											<option value={el.id} key={el.id}>
												{el.degree}
											</option>
										))}
								</Select>
							</Field>
						)}
					</Row>
					<Row flex={[1, 1, 1]}>
						<Field label="رقم الجوال">
							<Input
								value={phoneNumber}
								onChange={phoneNumberChangeHandler}
								type="number"
							/>
						</Field>
						<Field label="الفرع" required>
							<Select value={location} onChange={locationChangeHandler}>
								<option value=""></option>
								<option value="الغيضة">الغيضة</option>
								<option value="حصوين">حصوين</option>
								<option value="حوف">حوف</option>
								<option value="سيحوت">سيحوت</option>
								<option value="شحن">شحن</option>
								<option value="قشن">قشن</option>
								<option value="نشطون">نشطون</option>
							</Select>
						</Field>
						<></>
					</Row>
				</Card>
			</div>
		</form>
	);
};

export default BeneficiaryFormPage;
