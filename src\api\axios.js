import axios from "axios";

export const serverApi = axios.create({
	baseURL: "http://localhost:8060/api/v1/",
});

// Request interceptor to add Authorization header from localStorage token
serverApi.interceptors.request.use(
	(config) => {
		const token = localStorage.getItem("token");
		if (token) {
			config.headers.Authorization = `Bearer ${token}`;
		}
		const year = localStorage.getItem("year");
		config.headers["X-Year"] = year ? year : null;
		return config;
	},
	(error) => Promise.reject(error)
);
