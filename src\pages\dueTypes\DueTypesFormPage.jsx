import { But<PERSON>, Field, Input, Select } from "@fluentui/react-components";
import Row from "../../UI/row/Row";
import Card from "../../UI/card/Card";
import { DismissRegular, SaveRegular } from "@fluentui/react-icons";
import { useLocation, useNavigate } from "react-router-dom";
import { useState } from "react";
import { useMutation, useQuery } from "react-query";
import { addDueType, ediDueType, getDueType } from "../../api/serverApi";
import { toast } from "react-toastify";
import TopBar from "../../components/TopBar/TopBar";
const DueTypesFormPage = () => {
	//hooks
	const navigate = useNavigate();
	const info = useLocation();
	//states
	const [name, setName] = useState("");
	//queries
	useQuery({
		queryKey: ["dueType", info.state?.id],
		queryFn: getDueType,
		onSuccess: (res) => {
			setName(res.data.type.name);
		},
		enabled: !!info.state,
	});
	const addMutation = useMutation({
		mutationFn: addDueType,
		onSuccess: (res) => {
			toast.success("تمت الاضافة بنجاح", {
				position: toast.POSITION.TOP_CENTER,
			});
			navigate("./..");
		},
		onError: (err) => {
			toast.error(err.response.data.message, {
				position: toast.POSITION.TOP_CENTER,
			});
		},
	});
	const editMutation = useMutation({
		mutationFn: ediDueType,
		onSuccess: (res) => {
			toast.success("تم التعديل بنجاح", {
				position: toast.POSITION.TOP_CENTER,
			});
			navigate("./..", {});
		},
		onError: (err) => {
			toast.error(err.response.data.message, {
				position: toast.POSITION.TOP_CENTER,
			});
		},
	});
	//functions
	const nameChangeHandler = (e) => {
		setName(e.target.value);
	};

	return (
		<form
			onSubmit={(e) => {
				e.preventDefault();

				{
					info.state
						? editMutation.mutate({
								id: info.state.id,
								name: name.replace(/\s+/g, " ").trim(),
						  })
						: addMutation.mutate({
								name: name.replace(/\s+/g, " ").trim(),
						  });
				}
			}}
		>
			<TopBar
				right={
					<>
						<Button
							appearance="secondary"
							icon={<DismissRegular />}
							onClick={() => {
								navigate("./..");
							}}
						>
							الغاء
						</Button>
						<Button
							appearance="primary"
							icon={<SaveRegular />}
							type="submit"
							disabled={addMutation.isLoading || editMutation.isLoading}
						>
							حفظ
						</Button>
					</>
				}
			/>
			<div
				style={{
					padding: "0 5px",
					marginTop: "80px",
					paddingBottom: "120px",
					display: "flex",
					flexDirection: "column",
					gap: "15px",
				}}
			>
				<Card title="بيانات الاستحقاق">
					<Row flex={[2, 1, 1]}>
						<Field label="الاسم" required>
							<Input value={name} onChange={nameChangeHandler} required />
						</Field>
					</Row>
				</Card>
			</div>
		</form>
	);
};

export default DueTypesFormPage;
