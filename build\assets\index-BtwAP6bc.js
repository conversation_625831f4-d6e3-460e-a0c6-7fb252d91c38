import{M as k,n as Le,a as Ct,c as z,p as Te,i as _e,b as Pt,t as Ft,d as Kn,v as Nn,e as Bn,f as Ln,g as _n,h as Gn,j as K,k as Un,l as jn,m as he,o as tt,r as Dt,q as Wn,s as $n,u as Hn,w as Ot,x as zn,y as qn,z as Yn,A as Xn,B as Zn,C as Jn,D as It,E as Et,F as Rt,G as kt}from"./index-DUzCy4vr.js";function Qn(t,e){t.indexOf(e)===-1&&t.push(e)}function es(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}let re=()=>{};const Kt=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),Nt=t=>/^0[^.\s]+$/u.test(t);function Ge(t){let e;return()=>(e===void 0&&(e=t()),e)}const q=t=>t,ts=(t,e)=>n=>e(t(n)),fe=(...t)=>t.reduce(ts),Bt=(t,e,n)=>{const s=e-t;return s===0?1:(n-t)/s};class Lt{constructor(){this.subscriptions=[]}add(e){return Qn(this.subscriptions,e),()=>es(this.subscriptions,e)}notify(e,n,s){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,s);else for(let r=0;r<i;r++){const a=this.subscriptions[r];a&&a(e,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const E=t=>t*1e3,R=t=>t/1e3;function _t(t,e){return e?t*(1e3/e):0}const Gt=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,ns=1e-7,ss=12;function is(t,e,n,s,i){let r,a,o=0;do a=e+(n-e)/2,r=Gt(a,s,i)-t,r>0?n=a:e=a;while(Math.abs(r)>ns&&++o<ss);return a}function te(t,e,n,s){if(t===e&&n===s)return q;const i=r=>is(r,0,1,t,n);return r=>r===0||r===1?r:Gt(i(r),e,s)}const Ut=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,jt=t=>e=>1-t(1-e),Wt=te(.33,1.53,.69,.99),Ue=jt(Wt),$t=Ut(Ue),Ht=t=>(t*=2)<1?.5*Ue(t):.5*(2-Math.pow(2,-10*(t-1))),je=t=>1-Math.sin(Math.acos(t)),rs=jt(je),zt=Ut(je),as=te(.42,0,1,1),os=te(0,0,.58,1),qt=te(.42,0,.58,1),us=t=>Array.isArray(t)&&typeof t[0]!="number",Yt=t=>Array.isArray(t)&&typeof t[0]=="number",nt={linear:q,easeIn:as,easeInOut:qt,easeOut:os,circIn:je,circInOut:zt,circOut:rs,backIn:Ue,backInOut:$t,backOut:Wt,anticipate:Ht},ls=t=>typeof t=="string",st=t=>{if(Yt(t)){re(t.length===4);const[e,n,s,i]=t;return te(e,n,s,i)}else if(ls(t))return re(nt[t]!==void 0),nt[t];return t},ne=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],it={value:null,addProjectionMetrics:null};function cs(t,e){let n=new Set,s=new Set,i=!1,r=!1;const a=new WeakSet;let o={delta:0,timestamp:0,isProcessing:!1},u=0;function c(h){a.has(h)&&(l.schedule(h),t()),u++,h(o)}const l={schedule:(h,f=!1,d=!1)=>{const g=d&&i?n:s;return f&&a.add(h),g.has(h)||g.add(h),h},cancel:h=>{s.delete(h),a.delete(h)},process:h=>{if(o=h,i){r=!0;return}i=!0,[n,s]=[s,n],n.forEach(c),e&&it.value&&it.value.frameloop[e].push(u),u=0,n.clear(),i=!1,r&&(r=!1,l.process(h))}};return l}const hs=40;function Xt(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},r=()=>n=!0,a=ne.reduce((y,w)=>(y[w]=cs(r,e?w:void 0),y),{}),{setup:o,read:u,resolveKeyframes:c,preUpdate:l,update:h,preRender:f,render:d,postRender:T}=a,g=()=>{const y=k.useManualTiming?i.timestamp:performance.now();n=!1,k.useManualTiming||(i.delta=s?1e3/60:Math.max(Math.min(y-i.timestamp,hs),1)),i.timestamp=y,i.isProcessing=!0,o.process(i),u.process(i),c.process(i),l.process(i),h.process(i),f.process(i),d.process(i),T.process(i),i.isProcessing=!1,n&&e&&(s=!1,t(g))},v=()=>{n=!0,s=!0,i.isProcessing||t(g)};return{schedule:ne.reduce((y,w)=>{const m=a[w];return y[w]=(A,M=!1,b=!1)=>(n||v(),m.schedule(A,M,b)),y},{}),cancel:y=>{for(let w=0;w<ne.length;w++)a[ne[w]].cancel(y)},state:i,steps:a}}const{schedule:I,cancel:Ve,state:ae,steps:zr}=Xt(typeof requestAnimationFrame<"u"?requestAnimationFrame:q,!0);let se;function fs(){se=void 0}const O={now:()=>(se===void 0&&O.set(ae.isProcessing||k.useManualTiming?ae.timestamp:performance.now()),se),set:t=>{se=t,queueMicrotask(fs)}},J=t=>Math.round(t*1e5)/1e5,We=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function ds(t){return t==null}const ps=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,$e=(t,e)=>n=>!!(typeof n=="string"&&ps.test(n)&&n.startsWith(t)||e&&!ds(n)&&Object.prototype.hasOwnProperty.call(n,e)),Zt=(t,e,n)=>s=>{if(typeof s!="string")return s;const[i,r,a,o]=s.match(We);return{[t]:parseFloat(i),[e]:parseFloat(r),[n]:parseFloat(a),alpha:o!==void 0?parseFloat(o):1}},ms=t=>z(0,255,t),pe={...Le,transform:t=>Math.round(ms(t))},L={test:$e("rgb","red"),parse:Zt("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+pe.transform(t)+", "+pe.transform(e)+", "+pe.transform(n)+", "+J(Ct.transform(s))+")"};function gs(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}}const Ae={test:$e("#"),parse:gs,transform:L.transform},$={test:$e("hsl","hue"),parse:Zt("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+Te.transform(J(e))+", "+Te.transform(J(n))+", "+J(Ct.transform(s))+")"},x={test:t=>L.test(t)||Ae.test(t)||$.test(t),parse:t=>L.test(t)?L.parse(t):$.test(t)?$.parse(t):Ae.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?L.transform(t):$.transform(t),getAnimatableNone:t=>{const e=x.parse(t);return e.alpha=0,x.transform(e)}},ys=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function bs(t){var e,n;return isNaN(t)&&typeof t=="string"&&(((e=t.match(We))==null?void 0:e.length)||0)+(((n=t.match(ys))==null?void 0:n.length)||0)>0}const Jt="number",Qt="color",vs="var",Ts="var(",rt="${}",Vs=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ee(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let r=0;const o=e.replace(Vs,u=>(x.test(u)?(s.color.push(r),i.push(Qt),n.push(x.parse(u))):u.startsWith(Ts)?(s.var.push(r),i.push(vs),n.push(u)):(s.number.push(r),i.push(Jt),n.push(parseFloat(u))),++r,rt)).split(rt);return{values:n,split:o,indexes:s,types:i}}function en(t){return ee(t).values}function tn(t){const{split:e,types:n}=ee(t),s=e.length;return i=>{let r="";for(let a=0;a<s;a++)if(r+=e[a],i[a]!==void 0){const o=n[a];o===Jt?r+=J(i[a]):o===Qt?r+=x.transform(i[a]):r+=i[a]}return r}}const As=t=>typeof t=="number"?0:x.test(t)?x.getAnimatableNone(t):t;function ws(t){const e=en(t);return tn(t)(e.map(As))}const Y={test:bs,parse:en,createTransformer:tn,getAnimatableNone:ws};function me(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Ss({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,e/=100,n/=100;let i=0,r=0,a=0;if(!e)i=r=a=n;else{const o=n<.5?n*(1+e):n+e-n*e,u=2*n-o;i=me(u,o,t+1/3),r=me(u,o,t),a=me(u,o,t-1/3)}return{red:Math.round(i*255),green:Math.round(r*255),blue:Math.round(a*255),alpha:s}}function oe(t,e){return n=>n>0?e:t}const de=(t,e,n)=>t+(e-t)*n,ge=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},xs=[Ae,L,$],Ms=t=>xs.find(e=>e.test(t));function at(t){const e=Ms(t);if(!e)return!1;let n=e.parse(t);return e===$&&(n=Ss(n)),n}const ot=(t,e)=>{const n=at(t),s=at(e);if(!n||!s)return oe(t,e);const i={...n};return r=>(i.red=ge(n.red,s.red,r),i.green=ge(n.green,s.green,r),i.blue=ge(n.blue,s.blue,r),i.alpha=de(n.alpha,s.alpha,r),L.transform(i))},we=new Set(["none","hidden"]);function Cs(t,e){return we.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function Ps(t,e){return n=>de(t,e,n)}function He(t){return typeof t=="number"?Ps:typeof t=="string"?_e(t)?oe:x.test(t)?ot:Os:Array.isArray(t)?nn:typeof t=="object"?x.test(t)?ot:Fs:oe}function nn(t,e){const n=[...t],s=n.length,i=t.map((r,a)=>He(r)(r,e[a]));return r=>{for(let a=0;a<s;a++)n[a]=i[a](r);return n}}function Fs(t,e){const n={...t,...e},s={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(s[i]=He(t[i])(t[i],e[i]));return i=>{for(const r in s)n[r]=s[r](i);return n}}function Ds(t,e){const n=[],s={color:0,var:0,number:0};for(let i=0;i<e.values.length;i++){const r=e.types[i],a=t.indexes[r][s[r]],o=t.values[a]??0;n[i]=o,s[r]++}return n}const Os=(t,e)=>{const n=Y.createTransformer(e),s=ee(t),i=ee(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?we.has(t)&&!i.values.length||we.has(e)&&!s.values.length?Cs(t,e):fe(nn(Ds(s,i),i.values),n):oe(t,e)};function sn(t,e,n){return typeof t=="number"&&typeof e=="number"&&typeof n=="number"?de(t,e,n):He(t)(t,e)}const Is=t=>{const e=({timestamp:n})=>t(n);return{start:(n=!0)=>I.update(e,n),stop:()=>Ve(e),now:()=>ae.isProcessing?ae.timestamp:O.now()}},rn=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let r=0;r<i;r++)s+=Math.round(t(r/(i-1))*1e4)/1e4+", ";return`linear(${s.substring(0,s.length-2)})`},ue=2e4;function ze(t){let e=0;const n=50;let s=t.next(e);for(;!s.done&&e<ue;)e+=n,s=t.next(e);return e>=ue?1/0:e}function Es(t,e=100,n){const s=n({...t,keyframes:[0,e]}),i=Math.min(ze(s),ue);return{type:"keyframes",ease:r=>s.next(i*r).value/e,duration:R(i)}}const Rs=5;function an(t,e,n){const s=Math.max(e-Rs,0);return _t(n-t(s),e-s)}const S={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},ye=.001;function ks({duration:t=S.duration,bounce:e=S.bounce,velocity:n=S.velocity,mass:s=S.mass}){let i,r,a=1-e;a=z(S.minDamping,S.maxDamping,a),t=z(S.minDuration,S.maxDuration,R(t)),a<1?(i=c=>{const l=c*a,h=l*t,f=l-n,d=Se(c,a),T=Math.exp(-h);return ye-f/d*T},r=c=>{const h=c*a*t,f=h*n+n,d=Math.pow(a,2)*Math.pow(c,2)*t,T=Math.exp(-h),g=Se(Math.pow(c,2),a);return(-i(c)+ye>0?-1:1)*((f-d)*T)/g}):(i=c=>{const l=Math.exp(-c*t),h=(c-n)*t+1;return-ye+l*h},r=c=>{const l=Math.exp(-c*t),h=(n-c)*(t*t);return l*h});const o=5/t,u=Ns(i,r,o);if(t=E(t),isNaN(u))return{stiffness:S.stiffness,damping:S.damping,duration:t};{const c=Math.pow(u,2)*s;return{stiffness:c,damping:a*2*Math.sqrt(s*c),duration:t}}}const Ks=12;function Ns(t,e,n){let s=n;for(let i=1;i<Ks;i++)s=s-t(s)/e(s);return s}function Se(t,e){return t*Math.sqrt(1-e*e)}const Bs=["duration","bounce"],Ls=["stiffness","damping","mass"];function ut(t,e){return e.some(n=>t[n]!==void 0)}function _s(t){let e={velocity:S.velocity,stiffness:S.stiffness,damping:S.damping,mass:S.mass,isResolvedFromDuration:!1,...t};if(!ut(t,Ls)&&ut(t,Bs))if(t.visualDuration){const n=t.visualDuration,s=2*Math.PI/(n*1.2),i=s*s,r=2*z(.05,1,1-(t.bounce||0))*Math.sqrt(i);e={...e,mass:S.mass,stiffness:i,damping:r}}else{const n=ks(t);e={...e,...n,mass:S.mass},e.isResolvedFromDuration=!0}return e}function le(t=S.visualDuration,e=S.bounce){const n=typeof t!="object"?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:s,restDelta:i}=n;const r=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],o={done:!1,value:r},{stiffness:u,damping:c,mass:l,duration:h,velocity:f,isResolvedFromDuration:d}=_s({...n,velocity:-R(n.velocity||0)}),T=f||0,g=c/(2*Math.sqrt(u*l)),v=a-r,p=R(Math.sqrt(u/l)),V=Math.abs(v)<5;s||(s=V?S.restSpeed.granular:S.restSpeed.default),i||(i=V?S.restDelta.granular:S.restDelta.default);let y;if(g<1){const m=Se(p,g);y=A=>{const M=Math.exp(-g*p*A);return a-M*((T+g*p*v)/m*Math.sin(m*A)+v*Math.cos(m*A))}}else if(g===1)y=m=>a-Math.exp(-p*m)*(v+(T+p*v)*m);else{const m=p*Math.sqrt(g*g-1);y=A=>{const M=Math.exp(-g*p*A),b=Math.min(m*A,300);return a-M*((T+g*p*v)*Math.sinh(b)+m*v*Math.cosh(b))/m}}const w={calculatedDuration:d&&h||null,next:m=>{const A=y(m);if(d)o.done=m>=h;else{let M=m===0?T:0;g<1&&(M=m===0?E(T):an(y,m,A));const b=Math.abs(M)<=s,P=Math.abs(a-A)<=i;o.done=b&&P}return o.value=o.done?a:A,o},toString:()=>{const m=Math.min(ze(w),ue),A=rn(M=>w.next(m*M).value,m,30);return m+"ms "+A},toTransition:()=>{}};return w}le.applyToOptions=t=>{const e=Es(t,100,le);return t.ease=e.ease,t.duration=E(e.duration),t.type="keyframes",t};function xe({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:r=500,modifyTarget:a,min:o,max:u,restDelta:c=.5,restSpeed:l}){const h=t[0],f={done:!1,value:h},d=b=>o!==void 0&&b<o||u!==void 0&&b>u,T=b=>o===void 0?u:u===void 0||Math.abs(o-b)<Math.abs(u-b)?o:u;let g=n*e;const v=h+g,p=a===void 0?v:a(v);p!==v&&(g=p-h);const V=b=>-g*Math.exp(-b/s),y=b=>p+V(b),w=b=>{const P=V(b),F=y(b);f.done=Math.abs(P)<=c,f.value=f.done?p:F};let m,A;const M=b=>{d(f.value)&&(m=b,A=le({keyframes:[f.value,T(f.value)],velocity:an(y,b,f.value),damping:i,stiffness:r,restDelta:c,restSpeed:l}))};return M(0),{calculatedDuration:null,next:b=>{let P=!1;return!A&&m===void 0&&(P=!0,w(b),M(b)),m!==void 0&&b>=m?A.next(b-m):(!P&&w(b),f)}}}function Gs(t,e,n){const s=[],i=n||k.mix||sn,r=t.length-1;for(let a=0;a<r;a++){let o=i(t[a],t[a+1]);if(e){const u=Array.isArray(e)?e[a]||q:e;o=fe(u,o)}s.push(o)}return s}function Us(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const r=t.length;if(re(r===e.length),r===1)return()=>e[0];if(r===2&&e[0]===e[1])return()=>e[1];const a=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());const o=Gs(e,s,i),u=o.length,c=l=>{if(a&&l<t[0])return e[0];let h=0;if(u>1)for(;h<t.length-2&&!(l<t[h+1]);h++);const f=Bt(t[h],t[h+1],l);return o[h](f)};return n?l=>c(z(t[0],t[r-1],l)):c}function js(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=Bt(0,e,s);t.push(de(n,1,i))}}function Ws(t){const e=[0];return js(e,t.length-1),e}function $s(t,e){return t.map(n=>n*e)}function Hs(t,e){return t.map(()=>e||qt).splice(0,t.length-1)}function Q({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=us(s)?s.map(st):st(s),r={done:!1,value:e[0]},a=$s(n&&n.length===e.length?n:Ws(e),t),o=Us(a,e,{ease:Array.isArray(i)?i:Hs(e,i)});return{calculatedDuration:t,next:u=>(r.value=o(u),r.done=u>=t,r)}}const zs=t=>t!==null;function qe(t,{repeat:e,repeatType:n="loop"},s,i=1){const r=t.filter(zs),o=i<0||e&&n!=="loop"&&e%2===1?0:r.length-1;return!o||s===void 0?r[o]:s}const qs={decay:xe,inertia:xe,tween:Q,keyframes:Q,spring:le};function on(t){typeof t.type=="string"&&(t.type=qs[t.type])}class Ye{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,n){return this.finished.then(e,n)}}const Ys=t=>t/100;class Xe extends Ye{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var s,i;const{motionValue:n}=this.options;n&&n.updatedAt!==O.now()&&this.tick(O.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(i=(s=this.options).onStop)==null||i.call(s))},this.options=e,this.initAnimation(),this.play(),e.autoplay===!1&&this.pause()}initAnimation(){const{options:e}=this;on(e);const{type:n=Q,repeat:s=0,repeatDelay:i=0,repeatType:r,velocity:a=0}=e;let{keyframes:o}=e;const u=n||Q;u!==Q&&typeof o[0]!="number"&&(this.mixKeyframes=fe(Ys,sn(o[0],o[1])),o=[0,100]);const c=u({...e,keyframes:o});r==="mirror"&&(this.mirroredGenerator=u({...e,keyframes:[...o].reverse(),velocity:-a})),c.calculatedDuration===null&&(c.calculatedDuration=ze(c));const{calculatedDuration:l}=c;this.calculatedDuration=l,this.resolvedDuration=l+i,this.totalDuration=this.resolvedDuration*(s+1)-i,this.generator=c}updateTime(e){const n=Math.round(e-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=n}tick(e,n=!1){const{generator:s,totalDuration:i,mixKeyframes:r,mirroredGenerator:a,resolvedDuration:o,calculatedDuration:u}=this;if(this.startTime===null)return s.next(0);const{delay:c=0,keyframes:l,repeat:h,repeatType:f,repeatDelay:d,type:T,onUpdate:g,finalKeyframe:v}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-i/this.speed,this.startTime)),n?this.currentTime=e:this.updateTime(e);const p=this.currentTime-c*(this.playbackSpeed>=0?1:-1),V=this.playbackSpeed>=0?p<0:p>i;this.currentTime=Math.max(p,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=i);let y=this.currentTime,w=s;if(h){const b=Math.min(this.currentTime,i)/o;let P=Math.floor(b),F=b%1;!F&&b>=1&&(F=1),F===1&&P--,P=Math.min(P,h+1),!!(P%2)&&(f==="reverse"?(F=1-F,d&&(F-=d/o)):f==="mirror"&&(w=a)),y=z(0,1,F)*o}const m=V?{done:!1,value:l[0]}:w.next(y);r&&(m.value=r(m.value));let{done:A}=m;!V&&u!==null&&(A=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const M=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&A);return M&&T!==xe&&(m.value=qe(l,this.options,v,this.speed)),g&&g(m.value),M&&this.finish(),m}then(e,n){return this.finished.then(e,n)}get duration(){return R(this.calculatedDuration)}get time(){return R(this.currentTime)}set time(e){var n;e=E(e),this.currentTime=e,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),(n=this.driver)==null||n.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(O.now());const n=this.playbackSpeed!==e;this.playbackSpeed=e,n&&(this.time=R(this.currentTime))}play(){var i,r;if(this.isStopped)return;const{driver:e=Is,startTime:n}=this.options;this.driver||(this.driver=e(a=>this.tick(a))),(r=(i=this.options).onPlay)==null||r.call(i);const s=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=s):this.holdTime!==null?this.startTime=s-this.holdTime:this.startTime||(this.startTime=n??s),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(O.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var e,n;this.notifyFinished(),this.teardown(),this.state="finished",(n=(e=this.options).onComplete)==null||n.call(e)}cancel(){var e,n;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(n=(e=this.options).onCancel)==null||n.call(e)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){var n;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(n=this.driver)==null||n.stop(),e.observe(this)}}function Xs(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const _=t=>t*180/Math.PI,Me=t=>{const e=_(Math.atan2(t[1],t[0]));return Ce(e)},Zs={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Me,rotateZ:Me,skewX:t=>_(Math.atan(t[1])),skewY:t=>_(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},Ce=t=>(t=t%360,t<0&&(t+=360),t),lt=Me,ct=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),ht=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),Js={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ct,scaleY:ht,scale:t=>(ct(t)+ht(t))/2,rotateX:t=>Ce(_(Math.atan2(t[6],t[5]))),rotateY:t=>Ce(_(Math.atan2(-t[2],t[0]))),rotateZ:lt,rotate:lt,skewX:t=>_(Math.atan(t[4])),skewY:t=>_(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Pe(t){return t.includes("scale")?1:0}function Fe(t,e){if(!t||t==="none")return Pe(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,i;if(n)s=Js,i=n;else{const o=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=Zs,i=o}if(!i)return Pe(e);const r=s[e],a=i[1].split(",").map(ei);return typeof r=="function"?r(a):a[r]}const Qs=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return Fe(n,e)};function ei(t){return parseFloat(t.trim())}const ft=t=>t===Le||t===Pt,ti=new Set(["x","y","z"]),ni=Ft.filter(t=>!ti.has(t));function si(t){const e=[];return ni.forEach(n=>{const s=t.getValue(n);s!==void 0&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e}const G={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>Fe(e,"x"),y:(t,{transform:e})=>Fe(e,"y")};G.translateX=G.x;G.translateY=G.y;const U=new Set;let De=!1,Oe=!1,Ie=!1;function un(){if(Oe){const t=Array.from(U).filter(s=>s.needsMeasurement),e=new Set(t.map(s=>s.element)),n=new Map;e.forEach(s=>{const i=si(s);i.length&&(n.set(s,i),s.render())}),t.forEach(s=>s.measureInitialState()),e.forEach(s=>{s.render();const i=n.get(s);i&&i.forEach(([r,a])=>{var o;(o=s.getValue(r))==null||o.set(a)})}),t.forEach(s=>s.measureEndState()),t.forEach(s=>{s.suspendedScrollY!==void 0&&window.scrollTo(0,s.suspendedScrollY)})}Oe=!1,De=!1,U.forEach(t=>t.complete(Ie)),U.clear()}function ln(){U.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(Oe=!0)})}function ii(){Ie=!0,ln(),un(),Ie=!1}class Ze{constructor(e,n,s,i,r,a=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=n,this.name=s,this.motionValue=i,this.element=r,this.isAsync=a}scheduleResolve(){this.state="scheduled",this.isAsync?(U.add(this),De||(De=!0,I.read(ln),I.resolveKeyframes(un))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:n,element:s,motionValue:i}=this;if(e[0]===null){const r=i==null?void 0:i.get(),a=e[e.length-1];if(r!==void 0)e[0]=r;else if(s&&n){const o=s.readValue(n,a);o!=null&&(e[0]=o)}e[0]===void 0&&(e[0]=a),i&&r===void 0&&i.set(e[0])}Xs(e)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),U.delete(this)}cancel(){this.state==="scheduled"&&(U.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const ri=t=>t.startsWith("--");function ai(t,e,n){ri(e)?t.style.setProperty(e,n):t.style[e]=n}const oi=Ge(()=>window.ScrollTimeline!==void 0),ui={};function li(t,e){const n=Ge(t);return()=>ui[e]??n()}const cn=li(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),Z=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,dt={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Z([0,.65,.55,1]),circOut:Z([.55,0,1,.45]),backIn:Z([.31,.01,.66,-.59]),backOut:Z([.33,1.53,.69,.99])};function hn(t,e){if(t)return typeof t=="function"?cn()?rn(t,e):"ease-out":Yt(t)?Z(t):Array.isArray(t)?t.map(n=>hn(n,e)||dt.easeOut):dt[t]}function ci(t,e,n,{delay:s=0,duration:i=300,repeat:r=0,repeatType:a="loop",ease:o="easeOut",times:u}={},c=void 0){const l={[e]:n};u&&(l.offset=u);const h=hn(o,i);Array.isArray(h)&&(l.easing=h);const f={delay:s,duration:i,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:r+1,direction:a==="reverse"?"alternate":"normal"};return c&&(f.pseudoElement=c),t.animate(l,f)}function fn(t){return typeof t=="function"&&"applyToOptions"in t}function hi({type:t,...e}){return fn(t)&&cn()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class fi extends Ye{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;const{element:n,name:s,keyframes:i,pseudoElement:r,allowFlatten:a=!1,finalKeyframe:o,onComplete:u}=e;this.isPseudoElement=!!r,this.allowFlatten=a,this.options=e,re(typeof e.type!="string");const c=hi(e);this.animation=ci(n,s,i,c,r),c.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){const l=qe(i,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(l):ai(n,s,l),this.animation.cancel()}u==null||u(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var e,n;(n=(e=this.animation).finish)==null||n.call(e)}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:e}=this;e==="idle"||e==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var e,n;this.isPseudoElement||(n=(e=this.animation).commitStyles)==null||n.call(e)}get duration(){var n,s;const e=((s=(n=this.animation.effect)==null?void 0:n.getComputedTiming)==null?void 0:s.call(n).duration)||0;return R(Number(e))}get time(){return R(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=E(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:n}){var s;return this.allowFlatten&&((s=this.animation.effect)==null||s.updateTiming({easing:"linear"})),this.animation.onfinish=null,e&&oi()?(this.animation.timeline=e,q):n(this)}}const dn={anticipate:Ht,backInOut:$t,circInOut:zt};function di(t){return t in dn}function pi(t){typeof t.ease=="string"&&di(t.ease)&&(t.ease=dn[t.ease])}const pt=10;class mi extends fi{constructor(e){pi(e),on(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){const{motionValue:n,onUpdate:s,onComplete:i,element:r,...a}=this.options;if(!n)return;if(e!==void 0){n.set(e);return}const o=new Xe({...a,autoplay:!1}),u=E(this.finishedTime??this.time);n.setWithVelocity(o.sample(u-pt).value,o.sample(u).value,pt),o.stop()}}const mt=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(Y.test(t)||t==="0")&&!t.startsWith("url("));function gi(t){const e=t[0];if(t.length===1)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}function yi(t,e,n,s){const i=t[0];if(i===null)return!1;if(e==="display"||e==="visibility")return!0;const r=t[t.length-1],a=mt(i,e),o=mt(r,e);return!a||!o?!1:gi(t)||(n==="spring"||fn(n))&&s}function Ee(t){t.duration=0,t.type}const bi=new Set(["opacity","clipPath","filter","transform"]),vi=Ge(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function Ti(t){var l;const{motionValue:e,name:n,repeatDelay:s,repeatType:i,damping:r,type:a}=t;if(!(((l=e==null?void 0:e.owner)==null?void 0:l.current)instanceof HTMLElement))return!1;const{onUpdate:u,transformTemplate:c}=e.owner.getProps();return vi()&&n&&bi.has(n)&&(n!=="transform"||!c)&&!u&&!s&&i!=="mirror"&&r!==0&&a!=="inertia"}const Vi=40;class Ai extends Ye{constructor({autoplay:e=!0,delay:n=0,type:s="keyframes",repeat:i=0,repeatDelay:r=0,repeatType:a="loop",keyframes:o,name:u,motionValue:c,element:l,...h}){var T;super(),this.stop=()=>{var g,v;this._animation&&(this._animation.stop(),(g=this.stopTimeline)==null||g.call(this)),(v=this.keyframeResolver)==null||v.cancel()},this.createdAt=O.now();const f={autoplay:e,delay:n,type:s,repeat:i,repeatDelay:r,repeatType:a,name:u,motionValue:c,element:l,...h},d=(l==null?void 0:l.KeyframeResolver)||Ze;this.keyframeResolver=new d(o,(g,v,p)=>this.onKeyframesResolved(g,v,f,!p),u,c,l),(T=this.keyframeResolver)==null||T.scheduleResolve()}onKeyframesResolved(e,n,s,i){this.keyframeResolver=void 0;const{name:r,type:a,velocity:o,delay:u,isHandoff:c,onUpdate:l}=s;this.resolvedAt=O.now(),yi(e,r,a,o)||((k.instantAnimations||!u)&&(l==null||l(qe(e,s,n))),e[0]=e[e.length-1],Ee(s),s.repeat=0);const f={startTime:i?this.resolvedAt?this.resolvedAt-this.createdAt>Vi?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:n,...s,keyframes:e},d=!c&&Ti(f)?new mi({...f,element:f.motionValue.owner.current}):new Xe(f);d.finished.then(()=>this.notifyFinished()).catch(q),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(e,n){return this.finished.finally(e).then(()=>{})}get animation(){var e;return this._animation||((e=this.keyframeResolver)==null||e.resume(),ii()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var e;this._animation&&this.animation.cancel(),(e=this.keyframeResolver)==null||e.cancel()}}const wi=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Si(t){const e=wi.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}function pn(t,e,n=1){const[s,i]=Si(t);if(!s)return;const r=window.getComputedStyle(e).getPropertyValue(s);if(r){const a=r.trim();return Kt(a)?parseFloat(a):a}return _e(i)?pn(i,e,n+1):i}function mn(t,e){return(t==null?void 0:t[e])??(t==null?void 0:t.default)??t}const gn=new Set(["width","height","top","left","right","bottom",...Ft]),xi={test:t=>t==="auto",parse:t=>t},yn=t=>e=>e.test(t),bn=[Le,Pt,Te,Kn,Nn,Bn,xi],gt=t=>bn.find(yn(t));function Mi(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||Nt(t):!0}const Ci=new Set(["brightness","contrast","saturate","opacity"]);function Pi(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=n.match(We)||[];if(!s)return t;const i=n.replace(s,"");let r=Ci.has(e)?1:0;return s!==n&&(r*=100),e+"("+r+i+")"}const Fi=/\b([a-z-]*)\(.*?\)/gu,Re={...Y,getAnimatableNone:t=>{const e=t.match(Fi);return e?e.map(Pi).join(" "):t}},Di={...Ln,color:x,backgroundColor:x,outlineColor:x,fill:x,stroke:x,borderColor:x,borderTopColor:x,borderRightColor:x,borderBottomColor:x,borderLeftColor:x,filter:Re,WebkitFilter:Re},vn=t=>Di[t];function Tn(t,e){let n=vn(t);return n!==Re&&(n=Y),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Oi=new Set(["auto","none","0"]);function Ii(t,e,n){let s=0,i;for(;s<t.length&&!i;){const r=t[s];typeof r=="string"&&!Oi.has(r)&&ee(r).values.length&&(i=t[s]),s++}if(i&&n)for(const r of e)t[r]=Tn(n,i)}class Ei extends Ze{constructor(e,n,s,i,r){super(e,n,s,i,r,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:n,name:s}=this;if(!n||!n.current)return;super.readKeyframes();for(let u=0;u<e.length;u++){let c=e[u];if(typeof c=="string"&&(c=c.trim(),_e(c))){const l=pn(c,n.current);l!==void 0&&(e[u]=l),u===e.length-1&&(this.finalKeyframe=c)}}if(this.resolveNoneKeyframes(),!gn.has(s)||e.length!==2)return;const[i,r]=e,a=gt(i),o=gt(r);if(a!==o)if(ft(a)&&ft(o))for(let u=0;u<e.length;u++){const c=e[u];typeof c=="string"&&(e[u]=parseFloat(c))}else G[s]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:n}=this,s=[];for(let i=0;i<e.length;i++)(e[i]===null||Mi(e[i]))&&s.push(i);s.length&&Ii(e,s,n)}measureInitialState(){const{element:e,unresolvedKeyframes:n,name:s}=this;if(!e||!e.current)return;s==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=G[s](e.measureViewportBox(),window.getComputedStyle(e.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&e.getValue(s,i).jump(i,!1)}measureEndState(){var o;const{element:e,name:n,unresolvedKeyframes:s}=this;if(!e||!e.current)return;const i=e.getValue(n);i&&i.jump(this.measuredOrigin,!1);const r=s.length-1,a=s[r];s[r]=G[n](e.measureViewportBox(),window.getComputedStyle(e.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),(o=this.removedTransforms)!=null&&o.length&&this.removedTransforms.forEach(([u,c])=>{e.getValue(u).set(c)}),this.resolveNoneKeyframes()}}function Ri(t,e,n){if(t instanceof EventTarget)return[t];if(typeof t=="string"){const i=document.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}const yt=30,ki=t=>!isNaN(parseFloat(t));class Ki{constructor(e,n={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=s=>{var r;const i=O.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(s),this.current!==this.prev&&((r=this.events.change)==null||r.notify(this.current),this.dependents))for(const a of this.dependents)a.dirty()},this.hasAnimated=!1,this.setCurrent(e),this.owner=n.owner}setCurrent(e){this.current=e,this.updatedAt=O.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=ki(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new Lt);const s=this.events[e].add(n);return e==="change"?()=>{s(),I.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e){this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e)}setWithVelocity(e,n,s){this.set(n),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-s}jump(e,n=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var e;(e=this.events.change)==null||e.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=O.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>yt)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,yt);return _t(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var e,n;(e=this.dependents)==null||e.clear(),(n=this.events.destroy)==null||n.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ce(t,e){return new Ki(t,e)}const{schedule:Ni,cancel:qr}=Xt(queueMicrotask,!1),Bi={x:!1,y:!1};function Vn(){return Bi.y}function An(t,e){const n=Ri(t),s=new AbortController,i={passive:!0,...e,signal:s.signal};return[n,i,()=>s.abort()]}function bt(t){return!(t.pointerType==="touch"||Vn())}function Li(t,e,n={}){const[s,i,r]=An(t,n),a=o=>{if(!bt(o))return;const{target:u}=o,c=e(u,o);if(typeof c!="function"||!u)return;const l=h=>{bt(h)&&(c(h),u.removeEventListener("pointerleave",l))};u.addEventListener("pointerleave",l,i)};return s.forEach(o=>{o.addEventListener("pointerenter",a,i)}),r}const wn=(t,e)=>e?t===e?!0:wn(t,e.parentElement):!1,_i=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1,Gi=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function Ui(t){return Gi.has(t.tagName)||t.tabIndex!==-1}const ie=new WeakSet;function vt(t){return e=>{e.key==="Enter"&&t(e)}}function be(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}const ji=(t,e)=>{const n=t.currentTarget;if(!n)return;const s=vt(()=>{if(ie.has(n))return;be(n,"down");const i=vt(()=>{be(n,"up")}),r=()=>be(n,"cancel");n.addEventListener("keyup",i,e),n.addEventListener("blur",r,e)});n.addEventListener("keydown",s,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",s),e)};function Tt(t){return _i(t)&&!Vn()}function Wi(t,e,n={}){const[s,i,r]=An(t,n),a=o=>{const u=o.currentTarget;if(!Tt(o))return;ie.add(u);const c=e(u,o),l=(d,T)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",f),ie.has(u)&&ie.delete(u),Tt(d)&&typeof c=="function"&&c(d,{success:T})},h=d=>{l(d,u===window||u===document||n.useGlobalTarget||wn(u,d.target))},f=d=>{l(d,!1)};window.addEventListener("pointerup",h,i),window.addEventListener("pointercancel",f,i)};return s.forEach(o=>{(n.useGlobalTarget?window:o).addEventListener("pointerdown",a,i),_n(o)&&(o.addEventListener("focus",c=>ji(c,i)),!Ui(o)&&!o.hasAttribute("tabindex")&&(o.tabIndex=0))}),r}const $i=[...bn,x,Y],Hi=t=>$i.find(yn(t));function zi({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function qi(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}function Yi(t,e){return zi(qi(t.getBoundingClientRect(),e))}const Vt=()=>({min:0,max:0}),Sn=()=>({x:Vt(),y:Vt()}),ke={current:null},xn={current:!1};function Xi(){if(xn.current=!0,!!Gn)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>ke.current=t.matches;t.addEventListener("change",e),e()}else ke.current=!1}const Zi=new WeakMap;function Ji(t,e,n){for(const s in e){const i=e[s],r=n[s];if(K(i))t.addValue(s,i);else if(K(r))t.addValue(s,ce(i,{owner:t}));else if(r!==i)if(t.hasValue(s)){const a=t.getValue(s);a.liveStyle===!0?a.jump(i):a.hasAnimated||a.set(i)}else{const a=t.getStaticValue(s);t.addValue(s,ce(a!==void 0?a:i,{owner:t}))}}for(const s in n)e[s]===void 0&&t.removeValue(s);return e}const At=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Qi{scrapeMotionValuesFromProps(e,n,s){return{}}constructor({parent:e,props:n,presenceContext:s,reducedMotionConfig:i,blockInitialAnimation:r,visualState:a},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Ze,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const f=O.now();this.renderScheduledAt<f&&(this.renderScheduledAt=f,I.render(this.render,!1,!0))};const{latestValues:u,renderState:c}=a;this.latestValues=u,this.baseTarget={...u},this.initialValues=n.initial?{...u}:{},this.renderState=c,this.parent=e,this.props=n,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=Un(n),this.isVariantNode=jn(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:l,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const f in h){const d=h[f];u[f]!==void 0&&K(d)&&d.set(u[f])}}mount(e){var n;this.current=e,Zi.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((s,i)=>this.bindToMotionValue(i,s)),xn.current||Xi(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:ke.current,(n=this.parent)==null||n.addChild(this),this.update(this.props,this.presenceContext)}unmount(){var e;this.projection&&this.projection.unmount(),Ve(this.notifyUpdate),Ve(this.render),this.valueSubscriptions.forEach(n=>n()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),(e=this.parent)==null||e.removeChild(this);for(const n in this.events)this.events[n].clear();for(const n in this.features){const s=this.features[n];s&&(s.unmount(),s.isMounted=!1)}this.current=null}addChild(e){this.children.add(e),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(e)}removeChild(e){this.children.delete(e),this.enteringChildren&&this.enteringChildren.delete(e)}bindToMotionValue(e,n){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const s=he.has(e);s&&this.onBindTransform&&this.onBindTransform();const i=n.on("change",a=>{this.latestValues[e]=a,this.props.onUpdate&&I.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});let r;window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,e,n)),this.valueSubscriptions.set(e,()=>{i(),r&&r(),n.owner&&n.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e="animation";for(e in tt){const n=tt[e];if(!n)continue;const{isEnabled:s,Feature:i}=n;if(!this.features[e]&&i&&s(this.props)&&(this.features[e]=new i(this)),this.features[e]){const r=this.features[e];r.isMounted?r.update():(r.mount(),r.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):Sn()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let s=0;s<At.length;s++){const i=At[s];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const r="on"+i,a=e[r];a&&(this.propEventSubscriptions[i]=this.on(i,a))}this.prevMotionValues=Ji(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){const s=this.values.get(e);n!==s&&(s&&this.removeValue(e),this.bindToMotionValue(e,n),this.values.set(e,n),this.latestValues[e]=n.get())}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&n!==void 0&&(s=ce(n===null?void 0:n,{owner:this}),this.addValue(e,s)),s}readValue(e,n){let s=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options);return s!=null&&(typeof s=="string"&&(Kt(s)||Nt(s))?s=parseFloat(s):!Hi(s)&&Y.test(n)&&(s=Tn(e,n)),this.setBaseTarget(e,K(s)?s.get():s)),K(s)?s.get():s}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var r;const{initial:n}=this.props;let s;if(typeof n=="string"||typeof n=="object"){const a=Dt(this.props,n,(r=this.presenceContext)==null?void 0:r.custom);a&&(s=a[e])}if(n&&s!==void 0)return s;const i=this.getBaseTargetFromProps(this.props,e);return i!==void 0&&!K(i)?i:this.initialValues[e]!==void 0&&s===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new Lt),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}scheduleRenderMicrotask(){Ni.render(this.render)}}class Mn extends Qi{constructor(){super(...arguments),this.KeyframeResolver=Ei}sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:s}){delete n[e],delete s[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;K(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function Cn(t,{style:e,vars:n},s,i){const r=t.style;let a;for(a in e)r[a]=e[a];i==null||i.applyProjectionStyles(r,s);for(a in n)r.setProperty(a,n[a])}function er(t){return window.getComputedStyle(t)}class tr extends Mn{constructor(){super(...arguments),this.type="html",this.renderInstance=Cn}readValueFromInstance(e,n){var s;if(he.has(n))return(s=this.projection)!=null&&s.isProjecting?Pe(n):Qs(e,n);{const i=er(e),r=(Wn(n)?i.getPropertyValue(n):i[n])||0;return typeof r=="string"?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:n}){return Yi(e,n)}build(e,n,s){$n(e,n,s.transformTemplate)}scrapeMotionValuesFromProps(e,n,s){return Hn(e,n,s)}}const Pn=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function nr(t,e,n,s){Cn(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(Pn.has(i)?i:Ot(i),e.attrs[i])}class sr extends Mn{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Sn}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(he.has(n)){const s=vn(n);return s&&s.default||0}return n=Pn.has(n)?n:Ot(n),e.getAttribute(n)}scrapeMotionValuesFromProps(e,n,s){return zn(e,n,s)}build(e,n,s){qn(e,n,this.isSVGTag,s.transformTemplate,s.style)}renderInstance(e,n,s,i){nr(e,n,s,i)}mount(e){this.isSVGTag=Yn(e.tagName),super.mount(e)}}const ir=(t,e)=>Xn(t)?new sr(e):new tr(e,{allowProjection:t!==Zn.Fragment});function H(t,e,n){const s=t.getProps();return Dt(s,e,n!==void 0?n:s.custom,t)}const Ke=t=>Array.isArray(t);function rr(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,ce(n))}function ar(t){return Ke(t)?t[t.length-1]||0:t}function or(t,e){const n=H(t,e);let{transitionEnd:s={},transition:i={},...r}=n||{};r={...r,...s};for(const a in r){const o=ar(r[a]);rr(t,a,o)}}function ur(t){return!!(K(t)&&t.add)}function lr(t,e){const n=t.getValue("willChange");if(ur(n))return n.add(e);if(!n&&k.WillChange){const s=new k.WillChange("auto");t.addValue("willChange",s),s.add(e)}}function cr(t){return t.props[Jn]}const hr=t=>t!==null;function fr(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter(hr),r=e&&n!=="loop"&&e%2===1?0:i.length-1;return!r||s===void 0?i[r]:s}const dr={type:"spring",stiffness:500,damping:25,restSpeed:10},pr=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),mr={type:"keyframes",duration:.8},gr={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},yr=(t,{keyframes:e})=>e.length>2?mr:he.has(t)?t.startsWith("scale")?pr(e[1]):dr:gr;function br({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:r,repeatType:a,repeatDelay:o,from:u,elapsed:c,...l}){return!!Object.keys(l).length}const vr=(t,e,n,s={},i,r)=>a=>{const o=mn(s,t)||{},u=o.delay||s.delay||0;let{elapsed:c=0}=s;c=c-E(u);const l={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...o,delay:-c,onUpdate:f=>{e.set(f),o.onUpdate&&o.onUpdate(f)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:t,motionValue:e,element:r?void 0:i};br(o)||Object.assign(l,yr(t,l)),l.duration&&(l.duration=E(l.duration)),l.repeatDelay&&(l.repeatDelay=E(l.repeatDelay)),l.from!==void 0&&(l.keyframes[0]=l.from);let h=!1;if((l.type===!1||l.duration===0&&!l.repeatDelay)&&(Ee(l),l.delay===0&&(h=!0)),(k.instantAnimations||k.skipAnimations)&&(h=!0,Ee(l),l.delay=0),l.allowFlatten=!o.type&&!o.ease,h&&!r&&e.get()!==void 0){const f=fr(l.keyframes,o);if(f!==void 0){I.update(()=>{l.onUpdate(f),l.onComplete()});return}}return o.isSync?new Xe(l):new Ai(l)};function Tr({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,s}function Fn(t,e,{delay:n=0,transitionOverride:s,type:i}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:a,...o}=e;s&&(r=s);const u=[],c=i&&t.animationState&&t.animationState.getState()[i];for(const l in o){const h=t.getValue(l,t.latestValues[l]??null),f=o[l];if(f===void 0||c&&Tr(c,l))continue;const d={delay:n,...mn(r||{},l)},T=h.get();if(T!==void 0&&!h.isAnimating&&!Array.isArray(f)&&f===T&&!d.velocity)continue;let g=!1;if(window.MotionHandoffAnimation){const p=cr(t);if(p){const V=window.MotionHandoffAnimation(p,l,I);V!==null&&(d.startTime=V,g=!0)}}lr(t,l),h.start(vr(l,h,f,t.shouldReduceMotion&&gn.has(l)?{type:!1}:d,t,g));const v=h.animation;v&&u.push(v)}return a&&Promise.all(u).then(()=>{I.update(()=>{a&&or(t,a)})}),u}function Dn(t,e,n,s=0,i=1){const r=Array.from(t).sort((c,l)=>c.sortNodePosition(l)).indexOf(e),a=t.size,o=(a-1)*s;return typeof n=="function"?n(r,a):i===1?r*s:o-r*s}function Ne(t,e,n={}){var u;const s=H(t,e,n.type==="exit"?(u=t.presenceContext)==null?void 0:u.custom:void 0);let{transition:i=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(i=n.transitionOverride);const r=s?()=>Promise.all(Fn(t,s,n)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(c=0)=>{const{delayChildren:l=0,staggerChildren:h,staggerDirection:f}=i;return Vr(t,e,c,l,h,f,n)}:()=>Promise.resolve(),{when:o}=i;if(o){const[c,l]=o==="beforeChildren"?[r,a]:[a,r];return c().then(()=>l())}else return Promise.all([r(),a(n.delay)])}function Vr(t,e,n=0,s=0,i=0,r=1,a){const o=[];for(const u of t.variantChildren)u.notify("AnimationStart",e),o.push(Ne(u,e,{...a,delay:n+(typeof s=="function"?0:s)+Dn(t.variantChildren,u,s,i,r)}).then(()=>u.notify("AnimationComplete",e)));return Promise.all(o)}function Ar(t,e,n={}){t.notify("AnimationStart",e);let s;if(Array.isArray(e)){const i=e.map(r=>Ne(t,r,n));s=Promise.all(i)}else if(typeof e=="string")s=Ne(t,e,n);else{const i=typeof e=="function"?H(t,e,n.custom):e;s=Promise.all(Fn(t,i,n))}return s.then(()=>{t.notify("AnimationComplete",e)})}function On(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}const wr=Et.length;function In(t){if(!t)return;if(!t.isControllingVariants){const n=t.parent?In(t.parent)||{}:{};return t.props.initial!==void 0&&(n.initial=t.props.initial),n}const e={};for(let n=0;n<wr;n++){const s=Et[n],i=t.props[s];(It(i)||i===!1)&&(e[s]=i)}return e}const Sr=[...kt].reverse(),xr=kt.length;function Mr(t){return e=>Promise.all(e.map(({animation:n,options:s})=>Ar(t,n,s)))}function Cr(t){let e=Mr(t),n=wt(),s=!0;const i=u=>(c,l)=>{var f;const h=H(t,l,u==="exit"?(f=t.presenceContext)==null?void 0:f.custom:void 0);if(h){const{transition:d,transitionEnd:T,...g}=h;c={...c,...g,...T}}return c};function r(u){e=u(t)}function a(u){const{props:c}=t,l=In(t.parent)||{},h=[],f=new Set;let d={},T=1/0;for(let v=0;v<xr;v++){const p=Sr[v],V=n[p],y=c[p]!==void 0?c[p]:l[p],w=It(y),m=p===u?V.isActive:null;m===!1&&(T=v);let A=y===l[p]&&y!==c[p]&&w;if(A&&s&&t.manuallyAnimateOnMount&&(A=!1),V.protectedKeys={...d},!V.isActive&&m===null||!y&&!V.prevProp||Rt(y)||typeof y=="boolean")continue;const M=Pr(V.prevProp,y);let b=M||p===u&&V.isActive&&!A&&w||v>T&&w,P=!1;const F=Array.isArray(y)?y:[y];let j=F.reduce(i(p),{});m===!1&&(j={});const{prevResolvedValues:Je={}}=V,Rn={...Je,...j},Qe=C=>{b=!0,f.has(C)&&(P=!0,f.delete(C)),V.needsAnimating[C]=!0;const D=t.getValue(C);D&&(D.liveStyle=!1)};for(const C in Rn){const D=j[C],N=Je[C];if(d.hasOwnProperty(C))continue;let W=!1;Ke(D)&&Ke(N)?W=!On(D,N):W=D!==N,W?D!=null?Qe(C):f.add(C):D!==void 0&&f.has(C)?Qe(C):V.protectedKeys[C]=!0}V.prevProp=y,V.prevResolvedValues=j,V.isActive&&(d={...d,...j}),s&&t.blockInitialAnimation&&(b=!1);const et=A&&M;b&&(!et||P)&&h.push(...F.map(C=>{const D={type:p};if(typeof C=="string"&&s&&!et&&t.manuallyAnimateOnMount&&t.parent){const{parent:N}=t,W=H(N,C);if(N.enteringChildren&&W){const{delayChildren:kn}=W.transition||{};D.delay=Dn(N.enteringChildren,t,kn)}}return{animation:C,options:D}}))}if(f.size){const v={};if(typeof c.initial!="boolean"){const p=H(t,Array.isArray(c.initial)?c.initial[0]:c.initial);p&&p.transition&&(v.transition=p.transition)}f.forEach(p=>{const V=t.getBaseTarget(p),y=t.getValue(p);y&&(y.liveStyle=!0),v[p]=V??null}),h.push({animation:v})}let g=!!h.length;return s&&(c.initial===!1||c.initial===c.animate)&&!t.manuallyAnimateOnMount&&(g=!1),s=!1,g?e(h):Promise.resolve()}function o(u,c){var h;if(n[u].isActive===c)return Promise.resolve();(h=t.variantChildren)==null||h.forEach(f=>{var d;return(d=f.animationState)==null?void 0:d.setActive(u,c)}),n[u].isActive=c;const l=a(u);for(const f in n)n[f].protectedKeys={};return l}return{animateChanges:a,setActive:o,setAnimateFunction:r,getState:()=>n,reset:()=>{n=wt(),s=!0}}}function Pr(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!On(e,t):!1}function B(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function wt(){return{animate:B(!0),whileInView:B(),whileHover:B(),whileTap:B(),whileDrag:B(),whileFocus:B(),exit:B()}}class X{constructor(e){this.isMounted=!1,this.node=e}update(){}}class Fr extends X{constructor(e){super(e),e.animationState||(e.animationState=Cr(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();Rt(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),(e=this.unmountControls)==null||e.call(this)}}let Dr=0;class Or extends X{constructor(){super(...arguments),this.id=Dr++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n}=this.node.presenceContext,{isPresent:s}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===s)return;const i=this.node.animationState.setActive("exit",!e);n&&!e&&i.then(()=>{n(this.id)})}mount(){const{register:e,onExitComplete:n}=this.node.presenceContext||{};n&&n(this.id),e&&(this.unmount=e(this.id))}unmount(){}}const Ir={animation:{Feature:Fr},exit:{Feature:Or}};function St(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}function En(t){return{point:{x:t.pageX,y:t.pageY}}}function xt(t,e,n){const{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover",n==="Start");const i="onHover"+n,r=s[i];r&&I.postRender(()=>r(e,En(e)))}class Er extends X{mount(){const{current:e}=this.node;e&&(this.unmount=Li(e,(n,s)=>(xt(this.node,s,"Start"),i=>xt(this.node,i,"End"))))}unmount(){}}class Rr extends X{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=fe(St(this.node.current,"focus",()=>this.onFocus()),St(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Mt(t,e,n){const{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap",n==="Start");const i="onTap"+(n==="End"?"":n),r=s[i];r&&I.postRender(()=>r(e,En(e)))}class kr extends X{mount(){const{current:e}=this.node;e&&(this.unmount=Wi(e,(n,s)=>(Mt(this.node,s,"Start"),(i,{success:r})=>Mt(this.node,i,r?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Be=new WeakMap,ve=new WeakMap,Kr=t=>{const e=Be.get(t.target);e&&e(t)},Nr=t=>{t.forEach(Kr)};function Br({root:t,...e}){const n=t||document;ve.has(n)||ve.set(n,{});const s=ve.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(Nr,{root:t,...e})),s[i]}function Lr(t,e,n){const s=Br(e);return Be.set(t,n),s.observe(t),()=>{Be.delete(t),s.unobserve(t)}}const _r={some:0,all:1};class Gr extends X{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:s,amount:i="some",once:r}=e,a={root:n?n.current:void 0,rootMargin:s,threshold:typeof i=="number"?i:_r[i]},o=u=>{const{isIntersecting:c}=u;if(this.isInView===c||(this.isInView=c,r&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:l,onViewportLeave:h}=this.node.getProps(),f=c?l:h;f&&f(u)};return Lr(this.node.current,a,o)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(Ur(e,n))&&this.startObserver()}unmount(){}}function Ur({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const jr={inView:{Feature:Gr},tap:{Feature:kr},focus:{Feature:Rr},hover:{Feature:Er}},Wr={renderer:ir,...Ir,...jr};var Yr=Wr;export{Yr as default};
