import classes from "./sideBar.module.scss";
import { <PERSON> } from "react-router-dom";
import { useContext, useEffect, useRef, useState } from "react";
import logo from "../../assets/logo.png";

import {
	SettingsRegular,
	DocumentBulletListRegular,
	HomeRegular,
} from "@fluentui/react-icons";
import {
	Hamburger,
	NavCategory,
	NavCategoryItem,
	NavDrawer,
	NavDrawerBody,
	NavDrawerHeader,
	NavItem,
	NavSubItem,
	NavSubItemGroup,
} from "@fluentui/react-nav-preview";
import { AuthContext } from "../../store/auth-context";
import {
	Dialog,
	DialogActions,
	DialogBody,
	DialogContent,
	DialogSurface,
	DialogTitle,
	Tooltip,
	makeStyles,
} from "@fluentui/react-components";
import { useQuery } from "react-query";
import { getQR } from "../../api/serverApi";
const useStyles = makeStyles({
	root: {
		display: "flex",
		background: "#d9dee4",
		zIndex: "3 !important",
	},
	content: {
		display: "grid",
		justifyContent: "flex-start",
		alignItems: "flex-start",
	},
});

const SideBar = () => {
	const componentRef = useRef();

	const authCtx = useContext(AuthContext);
	const styles = useStyles();
	// const [status, setStatus] = useState("جاري التحقق...");
	const [isOpen, setIsOpen] = useState(true);
	const [type, setType] = useState("inline");
	const [isMultiple, setIsMultiple] = useState(false);
	const [dialog, setDialog] = useState({
		isOpened: false,
		title: "",
		content: "",
		actions: "",
	});
	const renderHamburgerWithToolTip = () => {
		return (
			<Tooltip content="Navigation" relationship="label">
				<Hamburger onClick={() => setIsOpen(!isOpen)} />
			</Tooltip>
		);
	};

	return (
		<div className={styles.root}>
			<Dialog
				open={dialog.isOpened}
				onOpenChange={(event, data) => {
					setDialog((prev) => {
						return { ...prev, isOpened: data.open };
					});
				}}
			>
				<DialogSurface>
					<DialogBody>
						<DialogTitle>{dialog.title}</DialogTitle>
						<DialogContent ref={componentRef}>{dialog.content}</DialogContent>
						<DialogActions>{dialog.actions}</DialogActions>
					</DialogBody>
				</DialogSurface>
			</Dialog>
			<NavDrawer
				defaultSelectedValue="2"
				defaultSelectedCategoryValue=""
				open={isOpen}
				type={type}
				multiple={isMultiple}
			>
				<NavDrawerHeader>{renderHamburgerWithToolTip()}</NavDrawerHeader>
				<NavDrawerBody className={styles.content}>
					<NavDrawerBody>
						<Link to="/">
							<div className={classes.logoContainer}>
								<img src={logo} alt="" className={classes.logo} />
							</div>
						</Link>
						<Link to="/home">
							<NavItem value="1" icon={<HomeRegular />}>
								الرئيسية
							</NavItem>
						</Link>
						{authCtx.permissions.addPay ||
						authCtx.permissions.deletePay ||
						authCtx.permissions.addSalaryAdvance ||
						authCtx.permissions.addReceive ||
						authCtx.permissions.deleteReceive ? (
							<NavCategory value="2">
								<NavCategoryItem icon={<SettingsRegular />}>
									الصندوق
								</NavCategoryItem>
								<NavSubItemGroup>
									{authCtx.permissions.addReceive ||
									authCtx.permissions.deleteReceive ? (
										<Link to="/receive">
											<NavSubItem value="3">القبض</NavSubItem>
										</Link>
									) : null}
									{authCtx.permissions.addPay ||
									authCtx.permissions.deletePay ? (
										<Link to="/pay/search">
											<NavSubItem value="4">الصرف</NavSubItem>
										</Link>
									) : null}
								</NavSubItemGroup>
							</NavCategory>
						) : null}
						{authCtx.permissions.addDuesList ||
						authCtx.permissions.editDuesList ||
						authCtx.permissions.deleteDuesList ||
						authCtx.permissions.editDuesListState ? (
							<Link to="/duesLists">
								<NavItem value="5" icon={<DocumentBulletListRegular />}>
									المستحقات
								</NavItem>
							</Link>
						) : null}
						{authCtx.permissions.receiveReports ? (
							<NavCategory value="6">
								<NavCategoryItem icon={<SettingsRegular />}>
									التقارير
								</NavCategoryItem>
								{authCtx.permissions.receiveReports ? (
									<NavSubItemGroup>
										<Link to="/ReceiveReport">
											<NavSubItem value="7">تقارير الاستلام</NavSubItem>
										</Link>
									</NavSubItemGroup>
								) : null}
							</NavCategory>
						) : null}
						{authCtx.permissions.addUser ||
						authCtx.permissions.editUser ||
						authCtx.permissions.deleteUser ||
						authCtx.permissions.addBeneficiary ||
						authCtx.permissions.editBeneficiary ||
						authCtx.permissions.deleteBeneficiary ? (
							<NavCategory value="8">
								<NavCategoryItem icon={<SettingsRegular />}>
									الاعدادات
								</NavCategoryItem>
								<NavSubItemGroup>
									{authCtx.permissions.addUser ||
									authCtx.permissions.editUser ||
									authCtx.permissions.deleteUser ? (
										<Link to="/users">
											<NavSubItem value="9">المستخدمين</NavSubItem>
										</Link>
									) : null}
									{authCtx.permissions.addBeneficiary ||
									authCtx.permissions.editBeneficiary ||
									authCtx.permissions.deleteBeneficiary ? (
										<>
											<Link to="/beneficiaries">
												<NavSubItem value="10">المستفيدين</NavSubItem>
											</Link>
											<Link to="/dueTypes">
												<NavSubItem value="11">انواع الاستحقاق</NavSubItem>
											</Link>
										</>
									) : null}
									<Link>
										<NavSubItem value="12">الاتصال بسنوات سابقة</NavSubItem>
									</Link>
								</NavSubItemGroup>
							</NavCategory>
						) : null}
					</NavDrawerBody>
				</NavDrawerBody>
			</NavDrawer>
			{!isOpen && (
				<div className={styles.content}>{renderHamburgerWithToolTip()}</div>
			)}
		</div>
	);
};

export default SideBar;
