{"name": "ypc-financial-fund", "private": true, "main": "public/main.js", "homepage": "./", "version": "1.0.0", "repository": {"type": "git", "url": "https://github.com/MohamedDev7/YPC-HR-COUPONS.git"}, "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "electron:dev": "npx electronmon --trace-warnings . ", "electron:build": "yarn build && electron-builder --win -c.extraMetadata.main=build/main.js", "electron:publish": "npm run build && electron-builder --win -c.extraMetadata.main=build/main.js --publish always"}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@fluentui/react-components": "^9.54.2", "@fluentui/react-datepicker-compat": "^0.4.44", "@fluentui/react-nav-preview": "^0.10.6", "@heroui/react": "^2.7.4", "@heroui/system": "^2.4.11", "@heroui/theme": "^2.4.11", "@mui/icons-material": "^5.15.3", "@mui/material": "^5.15.3", "@mui/x-data-grid": "^6.18.7", "@mynaui/icons-react": "^0.3.9", "@radix-ui/react-menubar": "^1.1.16", "@react-pdf/renderer": "^3.4.4", "android-sms-gateway": "^2.0.0", "axios": "^1.6.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "electron-context-menu": "^3.6.1", "electron-is-dev": "^2.0.0", "electron-updater": "^6.1.7", "framer-motion": "^12.4.7", "lucide-react": "^0.542.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-query": "^3.39.3", "react-router-dom": "^6.21.1", "react-to-print": "^2.14.15", "react-toastify": "^9.1.3", "sass": "^1.71.1", "socket.io-client": "^4.8.1", "stylis": "^4.3.1", "stylis-plugin-rtl": "^2.1.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.3", "use-react-router-breadcrumbs": "^4.0.1", "xlsx": "^0.18.5"}, "build": {"extends": null, "appId": "com.example.financial-fund", "productName": "financial fund", "icon": "./public/icon.png", "files": ["build/**/*", "node-module/**/*", "package.json"]}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.20", "electron": "^24.1.3", "electron-builder": "^23.6.0", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "globals": "^15.9.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^5.0.8"}}