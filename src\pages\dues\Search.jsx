import { useEffect, useState } from "react";

import {
	SearchRegular,
	DocumentBulletListRegular,
	MoneyHandRegular,
} from "@fluentui/react-icons";
import Row from "../../UI/row/Row";
import { useMutation, useQuery } from "react-query";
import {
	addPay,
	getAllClauses,
	getAllDuesLists,
	// PayDue,
	searchDues,
} from "../../api/serverApi";
import { toast } from "react-toastify";
import TopBar from "../../components/TopBar/TopBar";
import { useNavigate } from "react-router-dom";
import { useDebounce } from "use-debounce";
import {
	Button,
	Tooltip,
	Modal,
	ModalContent,
	ModalHeader,
	ModalBody,
	ModalFooter,
	useDisclosure,
	Input,
	Select,
	SelectItem,
	Switch,
	Table,
	TableHeader,
	TableColumn,
	TableBody,
	TableRow,
	TableCell,
	Card,
	CardHeader,
	CardBody,
} from "@heroui/react";

const Search = () => {
	//hooks
	const navigate = useNavigate();
	const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure();

	//states
	const [name, setName] = useState("");
	const [nameDebounce, setNameDebounce] = useDebounce(name, 500);
	const [receiverName, setReceiverName] = useState("");
	const [duesLists, setDuesLists] = useState([]);
	const [selectedDuesList, setSelectedDuesList] = useState("");

	const [searchBy, setSearchBy] = useState("name");
	const [clause, setClause] = useState("");

	const [content, setContnet] = useState("");

	// Modal state management (replacing dialog state)
	const [modalMode, setModalMode] = useState("");
	const [modalPayload, setModalPayload] = useState(null);
	const [selectedKeys, setSelectedKeys] = useState(new Set());
	const [selection, setSelection] = useState([]);
	const [amount, setAmount] = useState(0);
	const [tax, setTax] = useState(0);
	const [netAmount, setNetAmount] = useState(0);

	//queries
	const { data: clauses } = useQuery({
		queryKey: ["clauses"],
		queryFn: getAllClauses,
		select: (res) => res.data.clauses,
	});
	useQuery({
		queryKey: ["duesLists", 0, 0, "قابل للدفع", clause],
		queryFn: getAllDuesLists,
		select: (res) => {
			return res.data.duesLists.map((el) => el);
		},
		onSuccess: (data) => {
			setDuesLists(data);
		},
		enabled: searchBy === "duesList" && !!clause,
	});
	const {
		data: results,
		remove,
		isLoading,
		isFetching,
	} = useQuery({
		queryKey: ["results", searchBy, nameDebounce, selectedDuesList, clause],
		queryFn: searchDues,
		select: (res) => {
			console.log(`res`, res);
			return res.data.results;
		},
	});

	const payMutation = useMutation({
		mutationFn: addPay,
		onSuccess: (res) => {
			onClose();
			setModalMode("");
			setModalPayload(null);
			setName("");
			toast.success("تمت العملية بنجاح", {
				position: toast.POSITION.TOP_CENTER,
			});
			const data = {
				...res.data.data[0],
			};
			if (searchBy === "duesList") {
				navigate("./printDuesList", {
					state: {
						pay: data,
					},
				});
			} else {
				navigate("./printDues", {
					state: {
						pay: data,
					},
				});
			}
		},
		onError: (err) => {
			toast.error(err.response.data.message, {
				position: toast.POSITION.TOP_CENTER,
			});
		},
	});
	//functions

	const nameCHangeHandler = (e) => {
		setName(e.target.value);
	};

	// const onSwitchChange = (ev) => {
	// 	setDuesLists([]);
	// 	setSelectedDuesListId(null);
	// 	setReceiverName("");

	// };
	const onPayHandler = (data) => {
		payMutation.mutate(data);
	};
	// Table columns for HeroUI Table - no longer needed as separate definition
	useEffect(() => {
		if (searchBy === "name") {
			setContnet(
				<Input
					endContent={<SearchRegular />}
					onChange={nameCHangeHandler}
					value={name}
					size="sm"
					label="اسم المستفيد"
				/>
			);
		}
		// if (searchBy === "number") {
		// 	setContnet(
		// 		<Row flex={[2, 1]}>
		// 			<Input
		// 				value={number}
		// 				type="number"
		// 				label="رقم المستحق"
		// 				endContent={<SearchRegular />}
		// 				onChange={numberChangeHandler}
		// 			/>

		// 			<></>
		// 		</Row>
		// 	);
		// }
		if (searchBy === "duesList") {
			setContnet(
				duesLists ? (
					<div className="flex  gap-3">
						<Select
							placeholder="اختر كشف الاستحقاق"
							selectedKeys={[selectedDuesList]}
							size="sm"
							label="كشف الاستحقاق"
							onChange={(e) => {
								setName("");
								setNameDebounce("");
								remove();
								setSelection([]);
								setSelectedKeys(new Set());
								setAmount(0);
								setTax(0);
								setNetAmount(0);
								setSelectedDuesList(e.target.value);
							}}
						>
							{duesLists.map((el) => (
								<SelectItem key={el.id} value={el.id}>
									{el.title}
								</SelectItem>
							))}
						</Select>
					</div>
				) : (
					""
				)
			);
		}
	}, [searchBy, name, duesLists, receiverName, selectedDuesList]);
	useEffect(() => {
		let total = 0;
		let tax = 0;
		let net_amount = 0;
		selection.forEach((el) => {
			total = total + +el.amount;
			tax = tax + +el.tax;
			net_amount = net_amount + +el.net_amount;
		});
		setAmount(total);
		setTax(tax);
		setNetAmount(net_amount);
	}, [selection]);
	return (
		<div className="w-screen h-screen overflow-auto ">
			{/* Modal */}
			<Modal isOpen={isOpen} onOpenChange={onOpenChange} size="xl">
				<ModalContent>
					{() => (
						<>
							<ModalHeader className="flex flex-col gap-1">
								{modalMode === "payDues" && "صرف مستحقات"}
							</ModalHeader>
							<ModalBody>
								{modalMode === "payDues" && (
									<div>
										{searchBy === "duesList" ? (
											<div dir="rtl">
												{`هل انت متاكد من صرف المستحقات مقابل
												${selection[0]?.title} للمستفيدين:`}
												<ol className="flex flex-col gap-1 mt-2">
													{selection.map((el, i) => (
														<li key={i} className="flex gap-2 items-center">
															<div className="w-2 h-2 rounded-full bg-black" />
															{`${el.amount.toLocaleString()} ريال يمني لـ ${
																el.name
															}`}
														</li>
													))}
												</ol>
											</div>
										) : (
											<div dir="rtl">
												هل انت متاكد من صرف
												<span className="font-bold">
													{netAmount.toLocaleString()}
												</span>
												ريال يمني <span> </span>
												<span className="font-bold">ل{selection[0]?.name}</span>
												حسب التفاصيل التالية:
												<ol className="flex flex-col gap-1 mt-2">
													{selection.map((el, i) => (
														<li key={i} className="flex gap-2 items-center">
															<div className="w-2 h-2 rounded-full bg-black" />
															{`${el.amount.toLocaleString()} ريال يمني مقابل ${
																el.dues_list.title
															}`}
														</li>
													))}
												</ol>
											</div>
										)}
									</div>
								)}
							</ModalBody>
							<ModalFooter>
								{modalMode === "viewData" && (
									<Button color="primary" onPress={onClose}>
										تأكيد
									</Button>
								)}
								{modalMode === "payDues" && (
									<div className="flex gap-3">
										<Button variant="flat" color="warning" onPress={onClose}>
											الغاء
										</Button>
										<Button
											color="primary"
											isDisabled={payMutation.isLoading}
											onPress={() => {
												onPayHandler({
													selection,
													receiverName:
														searchBy === "duesList" ? receiverName : null,
													clause,
													amount,
													tax,
													net_amount: netAmount,
													paid_at: new Date().toLocaleString(),
												});
											}}
										>
											{searchBy === "duesList" ? "تأكيد" : "صرف"}
										</Button>
									</div>
								)}
							</ModalFooter>
						</>
					)}
				</ModalContent>
			</Modal>
			<TopBar
				right={
					<>
						<Button
							color="primary"
							startContent={<MoneyHandRegular />}
							isDisabled={selection.length === 0}
							onPress={() => {
								const testedNamed = selection.filter(
									(el) => el.name !== selection[0].name
								);
								if (testedNamed.length > 0 && searchBy !== "duesList") {
									toast.error("لا يمكن صرف مستحقات أكثر من شخص بنفس العملية", {
										position: toast.POSITION.TOP_CENTER,
									});
									return;
								}
								if (receiverName === "" && searchBy == "duesList") {
									toast.error("يجب ادخال اسم المستلم", {
										position: toast.POSITION.TOP_CENTER,
									});
									return;
								}
								if (selection.length > 4 && searchBy !== "duesList") {
									toast.error("لايمكن صرف أكثر من 4 مستحقات بنفس العملية", {
										position: toast.POSITION.TOP_CENTER,
									});
									return;
								}
								// Open modal for payment confirmation
								setModalMode("payDues");
								setModalPayload(null);
								onOpen();
							}}
						>
							صرف
						</Button>
						<Button
							color="primary"
							onPress={() => {
								console.log(`foo`, clause);
								console.log(`foo`, selectedDuesList);
								console.log(`foo`, searchBy);
							}}
						>
							test
						</Button>
					</>
				}
			/>
			<div className="w-full p-5 pb-16">
				<Card>
					<CardHeader className="bg-primary text-default-50 font-bold text-medium">
						معلومات البحث
					</CardHeader>
					<CardBody>
						<Row flex={[1, 1, 1, 1]}>
							<Select
								selectedKeys={[searchBy]}
								size="sm"
								label="البحث بواسطة"
								onSelectionChange={(keys) => {
									const selectedKey = Array.from(keys)[0];
									setName("");
									setNameDebounce("");
									setDuesLists([]);
									setReceiverName("");
									remove();
									setSelection([]);
									setSelectedKeys(new Set());
									setAmount(0);
									setTax(0);
									setNetAmount(0);
									setSelectedDuesList("");
									setSearchBy(selectedKey);
								}}
							>
								<SelectItem key="name" value="name">
									اسم المستفيد
								</SelectItem>
								<SelectItem key="duesList" value="duesList">
									كشف الاستحقاق
								</SelectItem>
							</Select>
							<Select
								selectedKeys={[clause]}
								label="البند"
								size="sm"
								onChange={(e) => {
									setName("");
									setNameDebounce("");
									setDuesLists([]);
									setReceiverName("");
									remove();
									setSelection([]);
									setSelectedKeys(new Set());
									setAmount(0);
									setTax(0);
									setNetAmount(0);
									setSelectedDuesList("");
									setClause(e.target.value);
								}}
							>
								{clauses &&
									clauses.map((el) => (
										<SelectItem className="text-right" key={el.id}>
											{el.name}
										</SelectItem>
									))}
							</Select>
							{content}
						</Row>
						{searchBy === "duesList" ? (
							<Row flex={[1, 1, 1, 1]}>
								<Input
									value={receiverName}
									label="اسم المستلم"
									size="sm"
									onChange={(e) => setReceiverName(e.target.value)}
								/>
							</Row>
						) : (
							<></>
						)}
					</CardBody>
				</Card>
				{results && results.length > 0 && (
					<Card>
						<CardHeader className="bg-primary text-default-50 font-bold text-medium">
							النتائج
						</CardHeader>
						<CardBody>
							<div className="my-5">
								{isLoading || isFetching ? (
									<p className="text-center">جاري معالجة البيانات ...</p>
								) : (
									<Table
										aria-label="نتائج البحث"
										selectionMode="multiple"
										selectedKeys={selectedKeys}
										onSelectionChange={(keys) => {
											setSelectedKeys(keys);
											// Convert selected keys to selection array
											if (typeof keys === "string" && keys === "all") {
												setSelection(results || []);
											} else {
												const selectedIds = Array.from(keys);
												const selectedItems =
													results?.filter((item) =>
														selectedIds.includes(item.id.toString())
													) || [];
												setSelection(selectedItems);
											}
										}}
									>
										<TableHeader>
											<TableColumn>م</TableColumn>
											<TableColumn>الاسم</TableColumn>
											<TableColumn>العنوان</TableColumn>
											<TableColumn>صافي المبلغ</TableColumn>
											<TableColumn>التاريخ</TableColumn>
										</TableHeader>
										<TableBody>
											{results && results.length > 0 ? (
												results.map((item) => (
													<TableRow key={item.id}>
														<TableCell>{item.id}</TableCell>
														<TableCell>{item.name}</TableCell>
														<TableCell>{item.dues_list.title}</TableCell>
														<TableCell>
															{item.net_amount.toLocaleString()} ريال يمني
														</TableCell>
														<TableCell>{item.dues_list.date}</TableCell>
													</TableRow>
												))
											) : (
												<TableRow>
													<TableCell colSpan={7} className="text-center">
														لا توجد نتائج
													</TableCell>
												</TableRow>
											)}
										</TableBody>
									</Table>
								)}
							</div>
						</CardBody>
					</Card>
				)}
			</div>
		</div>
	);
};

export default Search;
