import { useEffect, useState } from "react";

import {
	SearchRegular,
	DocumentBulletListRegular,
	MoneyHandRegular,
} from "@fluentui/react-icons";
import Row from "../../UI/row/Row";
import { useMutation, useQuery } from "react-query";
import {
	addPay,
	getAllDuesLists,
	// PayDue,
	searchDues,
} from "../../api/serverApi";
import { toast } from "react-toastify";
import TopBar from "../../components/TopBar/TopBar";
import Table from "../../UI/table/Table";
import { useNavigate } from "react-router-dom";
import { useDebounce } from "use-debounce";
import {
	Button,
	Tooltip,
	Modal,
	ModalContent,
	ModalHeader,
	ModalBody,
	ModalFooter,
	useDisclosure,
	Input,
	Select,
	SelectItem,

	Card,
	CardHeader,
	CardBody,
} from "@heroui/react";

const Search = () => {
	//hooks
	const navigate = useNavigate();
	const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure();

	//states
	const [name, setName] = useState("");
	const [nameDebounce, setNameDebounce] = useDebounce(name, 500);
	const [receiverName, setReceiverName] = useState("");
	const [number, setNumber] = useState("");
	const [numberDebounce, setNumberDebounce] = useDebounce(number, 500);
	const [duesLists, setDuesLists] = useState([]);
	const [selectedDuesListId, setSelectedDuesListId] = useState(null);
	const [searchOnlyUnpaied, setSearchOnlyUnpaied] = useState(true);
	const [searchBy, setSearchBy] = useState("name");
	const [clause, setClause] = useState("");
	const [clauseId, setClauseId] = useState("");
	// const [salaryAdvance, setSalaryAdvance] = useState([]);
	const [content, setContnet] = useState("");
	// const [paySalaryAdvance, setPaySalaryAdvance] = useState([]);

	// Modal state management (replacing dialog state)
	const [modalMode, setModalMode] = useState("");
	const [modalPayload, setModalPayload] = useState(null);
	const [selection, setSelection] = useState([]);
	const [selectionTotal, setSelectionTotal] = useState(0);

	//queries
	useQuery({
		queryKey: ["duesLists", "1", "payable", searchBy, searchOnlyUnpaied],
		queryFn: getAllDuesLists,
		select: (res) => {
			return res.data.duesLists.map((el) => {
				return { ...el };
			});
		},
		// enabled: false,
		enabled: searchBy === "duesList",
		onSuccess: (data) => {
			setDuesLists(data);
		},
	});
	const {
		data: results,
		remove,
		isLoading,
		isFetching,
	} = useQuery({
		queryKey: [
			"results",
			searchBy,
			nameDebounce,
			numberDebounce,
			searchOnlyUnpaied,
			selectedDuesListId?.value,
		],
		queryFn: searchDues,
		select: (res) => {
			return res.data.results.map((el) => {
				return {
					...el,
					stateAr: el.state === "paid" ? "مستلمة" : "غير مستلمة",
				};
			});
		},
		enabled: !!nameDebounce || !!numberDebounce || !!selectedDuesListId?.value,
	});

	const payMutation = useMutation({
		mutationFn: addPay,
		onSuccess: (res) => {
			onClose();
			setModalMode("");
			setModalPayload(null);
			setName("");
			setNumber("");
			toast.success("تمت العملية بنجاح", {
				position: toast.POSITION.TOP_CENTER,
			});
			const data = {
				...res.data.data[0],
			};
			if (searchBy === "duesList") {
				navigate("./printDuesList", {
					state: {
						pay: data,
					},
				});
			} else {
				navigate("./printDues", {
					state: {
						pay: data,
					},
				});
			}
		},
		onError: (err) => {
			toast.error(err.response.data.message, {
				position: toast.POSITION.TOP_CENTER,
			});
		},
	});
	//functions

	const nameCHangeHandler = (e) => {
		setName(e.target.value);
	};
	const numberChangeHandler = (e) => {
		setNumber(e.target.value);
	};
	const onSwitchChange = (ev) => {
		setDuesLists([]);
		setSelectedDuesListId(null);
		setReceiverName("");
		setSearchOnlyUnpaied(ev.currentTarget.checked);
	};
	const onPayHandler = (data) => {
		payMutation.mutate(data);
	};
	const columns = [
		{ field: "id", headerName: "م", width: "60" },
		{ field: "name", headerName: "الاسم", width: "250" },
		{ field: "title", headerName: "العنوان", width: "250" },
		{ field: "amount", headerName: "المبلغ", width: "100" },
		{ field: "date", headerName: "التاريخ", width: "100" },
		{ field: "clauseText", headerName: "البند", width: "100" },
		{
			field: "actions",
			headerName: "خيارات",
			width: "200",
			renderCell: (params) => {
				return (
					<Tooltip content="عرض البيانات" relationship="label">
						<Button
							color="warning"
							startContent={<DocumentBulletListRegular />}
							size="md"
							onPress={() => {
								setModalMode("viewData");
								setModalPayload(params.row);
								onOpen();
							}}
						/>
					</Tooltip>
				);
			},
		},
	];
	useEffect(() => {
		if (searchBy === "name") {
			setContnet(
				<Input
					endContent={<SearchRegular />}
					onChange={nameCHangeHandler}
					value={name}
					size="sm"
					label="اسم المستفيد"
				/>
			);
		}
		// if (searchBy === "number") {
		// 	setContnet(
		// 		<Row flex={[2, 1]}>
		// 			<Input
		// 				value={number}
		// 				type="number"
		// 				label="رقم المستحق"
		// 				endContent={<SearchRegular />}
		// 				onChange={numberChangeHandler}
		// 			/>

		// 			<></>
		// 		</Row>
		// 	);
		// }
		if (searchBy === "duesList") {
			setContnet(
				duesLists ? (
					<div className="flex  gap-3">
						<Select
							placeholder="اختر كشف الاستحقاق"
							selectedKeys={
								selectedDuesListId ? [selectedDuesListId.toString()] : []
							}
							size="sm"
							label="كشف الاستحقاق"
							onSelectionChange={(keys) => {
								const selectedKey = Array.from(keys)[0];
								setSelectedDuesListId(
									selectedKey ? { value: selectedKey } : null
								);
							}}
						>
							{duesLists.map((el) => (
								<SelectItem key={el.id} value={el.id}>
									{el.title}
								</SelectItem>
							))}
						</Select>

						{searchOnlyUnpaied ? (
							<Input
								value={receiverName}
								label="اسم المستلم"
								size="sm"
								onChange={(e) => setReceiverName(e.target.value)}
							/>
						) : (
							<></>
						)}
					</div>
				) : (
					""
				)
			);
		}
	}, [
		searchBy,
		name,
		number,
		duesLists,
		receiverName,
		searchOnlyUnpaied,
		selectedDuesListId,
	]);
	useEffect(() => {
		let total = 0;
		selection.forEach((el) => (total = total + el.amount));

		setSelectionTotal(total);
	}, [selection]);
	return (
		<div className="w-screen h-screen overflow-auto ">
			{/* Modal */}
			<Modal isOpen={isOpen} onOpenChange={onOpenChange} size="xl">
				<ModalContent>
					{() => (
						<>
							<ModalHeader className="flex flex-col gap-1">
								{modalMode === "viewData" && "عرض بيانات"}
								{modalMode === "payDues" && "صرف مستحقات"}
							</ModalHeader>
							<ModalBody>
								{modalMode === "viewData" && modalPayload && (
									<div className="flex flex-col gap-3 text-lg">
										<div className="flex gap-2">
											<span>رقم المستحق:</span>
											<span className="font-bold">{modalPayload.id}</span>
										</div>
										<div className="flex gap-2">
											<span>اسم المستفيد:</span>
											<span className="font-bold">{modalPayload.name}</span>
										</div>
										<div className="flex gap-2">
											<span>المبلغ:</span>
											<span className="font-bold">
												{modalPayload.amount.toLocaleString()} ريال يمني
											</span>
										</div>
										<div className="flex gap-2">
											<span>مقابل:</span>
											<span className="font-bold">{modalPayload.title}</span>
										</div>
										<div className="flex gap-2">
											<span>الحالة:</span>
											<span className="font-bold">
												{modalPayload.state === "unPaid"
													? "غير مستلمة"
													: "مستلمة"}
											</span>
										</div>
										<div className="flex gap-2">
											<span>رقم سند الصرف:</span>
											<span className="font-bold">
												{modalPayload.pay_id === null
													? "غير محدد"
													: modalPayload.pay_id}
											</span>
										</div>
										<div className="flex gap-2">
											<span>تاريخ ووقت الاستلام:</span>
											<span className="font-bold">
												{modalPayload.paid_at === null
													? "غير محدد"
													: modalPayload.paid_at}
											</span>
										</div>
									</div>
								)}
								{modalMode === "payDues" && (
									<div>
										{searchBy === "duesList" ? (
											<div>
												{`هل انت متاكد من صرف المستحقات مقابل
												${selection[0]?.title} للمستفيدين:`}
												<ol className="flex flex-col gap-1 mt-2">
													{selection.map((el, i) => (
														<li key={i} className="flex gap-2 items-center">
															<div className="w-2 h-2 rounded-full bg-black" />
															{`${el.amount.toLocaleString()} ريال يمني لـ ${
																el.name
															}`}
														</li>
													))}
												</ol>
											</div>
										) : (
											<div>
												هل انت متاكد من صرف المستحقات التالية ل{" "}
												{selection[0]?.name}
												<ol className="flex flex-col gap-1 mt-2">
													{selection.map((el, i) => (
														<li key={i} className="flex gap-2 items-center">
															<div className="w-2 h-2 rounded-full bg-black" />
															{`${el.amount.toLocaleString()} ريال يمني مقابل ${
																el.title
															}`}
														</li>
													))}
												</ol>
											</div>
										)}
									</div>
								)}
							</ModalBody>
							<ModalFooter>
								{modalMode === "viewData" && (
									<Button color="primary" onPress={onClose}>
										تأكيد
									</Button>
								)}
								{modalMode === "payDues" && (
									<div className="flex gap-3">
										<Button variant="flat" color="warning" onPress={onClose}>
											الغاء
										</Button>
										<Button
											color="primary"
											isDisabled={payMutation.isLoading}
											onPress={() => {
												onPayHandler({
													selection,
													receiverName:
														searchBy === "duesList" ? receiverName : undefined,
													has_portfolio: false,
													supplier: null,
													clause: clauseId,
													type: searchBy === "duesList" ? "duesList" : "dues",
													paid_at: new Date().toLocaleString(),
												});
											}}
										>
											{searchBy === "duesList" ? "تأكيد" : "صرف"}
										</Button>
									</div>
								)}
							</ModalFooter>
						</>
					)}
				</ModalContent>
			</Modal>
			<TopBar
				right={
					<>
						<Button
							color="primary"
							startContent={<MoneyHandRegular />}
							isDisabled={selection.length === 0 || !searchOnlyUnpaied}
							onPress={() => {
								const testedNamed = selection.filter(
									(el) => el.name !== selection[0].name
								);
								if (testedNamed.length > 0 && searchBy !== "duesList") {
									toast.error("لا يمكن صرف مستحقات أكثر من شخص بنفس العملية", {
										position: toast.POSITION.TOP_CENTER,
									});
									return;
								}
								if (receiverName === "" && searchBy == "duesList") {
									toast.error("يجب ادخال اسم المستلم", {
										position: toast.POSITION.TOP_CENTER,
									});
									return;
								}
								if (selection.length > 4 && searchBy !== "duesList") {
									toast.error("لايمكن صرف أكثر من 4 مستحقات بنفس العملية", {
										position: toast.POSITION.TOP_CENTER,
									});
									return;
								}
								// Open modal for payment confirmation
								setModalMode("payDues");
								setModalPayload(null);
								onOpen();
							}}
						>
							صرف
						</Button>
					</>
				}
			/>
			<div className="w-full p-5 pb-16">
				<Card>
					<CardHeader className="bg-primary text-default-50 font-bold text-medium">
						معلومات البحث
					</CardHeader>
					<CardBody>
						<Row flex={[1, 1, 1, 1]}>
							<Select
								selectedKeys={[searchBy]}
								size="sm"
								label="البحث بواسطة"
								onSelectionChange={(keys) => {
									const selectedKey = Array.from(keys)[0];
									setName("");
									setNameDebounce("");
									setNumber("");
									setNumberDebounce("");
									setDuesLists([]);
									setReceiverName("");
									remove();
									setSelection([]);
									setSelectionTotal(0);
									setSelectedDuesListId(null);
									setSearchBy(selectedKey);
								}}
							>
								<SelectItem key="name" value="name">
									اسم المستفيد
								</SelectItem>
								<SelectItem key="number" value="number">
									رقم المستحق
								</SelectItem>
								<SelectItem key="duesList" value="duesList">
									كشف الاستحقاق
								</SelectItem>
							</Select>
							{content}
							{/* <div className="flex items-center gap-2 mt-6">
								<span className="text-sm">
									{searchOnlyUnpaied
										? "المستحقات غير المستلمة"
										: "المستحقات المستلمة"}
								</span>
								<Switch
									isSelected={searchOnlyUnpaied}
									onValueChange={(checked) => {
										setSelection([]);
										setSelectionTotal(0);
										onSwitchChange({ currentTarget: { checked } });
									}}
								/>
							</div> */}
							<></>
						</Row>
					</CardBody>
				</Card>
				{results && results.length > 0 && (
					<Card>
						<CardHeader className="bg-primary text-default-50 font-bold text-medium">
							النتائج
						</CardHeader>
						<CardBody>
							<div style={{ margin: "20px 0" }}>
								{isLoading || isFetching ? (
									<p style={{ textAlign: "center" }}>
										{" "}
										جاري معالجة البيانات ...
									</p>
								) : (
									<Table
										columns={columns}
										rows={results}
										hideFooter={false}
										checkboxSelection={searchOnlyUnpaied ? true : false}
										onRowSelectionModelChange={setSelection}
										pageSize={100}
									/>
								)}
							</div>
						</CardBody>
					</Card>
				)}
			</div>
		</div>
	);
};

export default Search;
