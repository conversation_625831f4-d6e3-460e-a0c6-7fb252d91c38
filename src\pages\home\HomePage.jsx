import Card from "../../UI/card/Card";
import classes from "./homePage.module.scss";
import { getAllStatistics } from "../../api/serverApi";
import { useQuery } from "react-query";

const HomePage = () => {
	//queries

	const { data: statistics } = useQuery({
		queryKey: ["statistics"],
		queryFn: getAllStatistics,
		select: (res) => {
			return res.data.statistics;
		},
	});
	return (
		<>
			{statistics ? (
				<div className={classes.container}>
					<div style={{ display: "flex", gap: "15px" }}>
						<Card title={`اجمالي الرصيد`} width="500px">
							<div className={classes.card}>
								<div className={classes.number}>
									{statistics.balance.toLocaleString()}
								</div>
								<div className={classes.text}>ريال</div>
							</div>
						</Card>
						<Card title="المستحقات الغير مستلمة" width="350px">
							<div className={classes.card}>
								<div className={classes.number}>{statistics.unPaid}</div>
								<div className={classes.text}>مستحق</div>
							</div>
						</Card>
						<Card title="الكشوفات المفتوحة" width="350px">
							<div className={classes.card}>
								<div className={classes.number}>{statistics.openDocs}</div>
								<div className={classes.text}>كشوفات</div>
							</div>
						</Card>
					</div>
				</div>
			) : null}
		</>
	);
};

export default HomePage;
