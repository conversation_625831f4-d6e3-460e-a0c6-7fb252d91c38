import { serverApi } from "./axios";
//Clauses API
export const getAllClauses = async () => {
	const res = await serverApi.get(`/Clauses`);
	return res;
};
//Dues API
export const searchDues = async (data) => {
	if (data.queryKey[1] === "duesList" && !data.queryKey[5]) {
		return;
	}
	const res = await serverApi.get(
		`/search?searchBy=${data.queryKey[1]}&name=${data.queryKey[2]}&number=${data.queryKey[3]}&searchOnlyUnpaied=${data.queryKey[4]}&selectedDuesListId=${data.queryKey[5]}`
	);
	return res;
};

export const getDuesByMonthAndType = async (data) => {
	const res = await serverApi.get(
		`/dues/${data.queryKey[1]}/${data.queryKey[2]}`
	);
	return res;
};
export const getDuesByDuesListsIds = async (data) => {
	const res = await serverApi.post(`/dues/ids`, data.queryKey[1]);
	return res;
};
export const getDuesByPayId = async (data) => {
	const res = await serverApi.get(`/dues/pay/${data.queryKey[1]}`);
	return res;
};

//Beneficiaries API
export const addBeneficiary = async (data) => {
	const res = await serverApi.post("/beneficiaries", data);
	return res;
};
export const getAllBeneficiaries = async () => {
	const res = await serverApi.get("/beneficiaries");
	return res;
};
export const deleteBeneficiary = async (id) => {
	const res = await serverApi.delete(`/beneficiaries/${id}`);
	return res;
};
export const ediBeneficiary = async (data) => {
	const res = await serverApi.patch(`/beneficiaries/${data.id}`, data);
	return res;
};
export const getBeneficiary = async (data) => {
	const res = await serverApi.get(`/beneficiaries/${data.queryKey[1]}`);
	return res;
};
//due types API
export const addDueType = async (data) => {
	const res = await serverApi.post("/dueTypes", data);
	return res;
};
export const getAllDueTypes = async () => {
	const res = await serverApi.get("/dueTypes");
	return res;
};
export const deleteDueType = async (id) => {
	const res = await serverApi.delete(`/dueTypes/${id}`);
	return res;
};
export const ediDueType = async (data) => {
	const res = await serverApi.patch(`/dueTypes/${data.id}`, data);
	return res;
};
export const getDueType = async (data) => {
	const res = await serverApi.get(`/dueTypes/${data.queryKey[1]}`);
	return res;
};
//Dues Lists API
export const addDuesList = async (data) => {
	const res = await serverApi.post("/duesLists", data);
	return res;
};
export const addDuesListUsingExcel = async (data) => {
	const res = await serverApi.post("/duesLists/excel", data);
	return res;
};
export const getAllDuesLists = async (data) => {
	const res = await serverApi.get(
		`/duesLists?page=${data.queryKey[1]}&limit=${data.queryKey[2]}&state=${data.queryKey[3]}&clause=${data.queryKey[4]}`
	);
	return res;
};

export const getDuesListById = async (data) => {
	const res = await serverApi.get(`/duesLists/${data.queryKey[1]}`);
	return res;
};
export const updateDuesList = async (data) => {
	const res = await serverApi.patch(`/duesLists/${data.id}`, data);
	return res;
};
export const updateDuesListUsingExcel = async (data) => {
	const res = await serverApi.patch(`/duesLists/excel/${data.id}`, data);
	return res;
};
export const updateDuesListState = async (data) => {
	console.log(`data`, data);
	const res = await serverApi.patch(`/duesLists/state`, data);
	return res;
};
export const approveDuesList = async (data) => {
	const res = await serverApi.post(`/duesLists/approve/${data.id}`, data);
	return res;
};
export const deleteDuesLists = async (ids) => {
	console.log(`ids`, ids);
	const res = await serverApi.delete(`/duesLists?ids=${ids}`);
	return res;
};
//Statistics API
export const getAllStatistics = async () => {
	const res = await serverApi.get("/statistics");
	return res;
};
//Receive API
export const addReceive = async (data) => {
	const res = await serverApi.post(`/receives`, data);
	return res;
};
export const addMultiReceives = async (data) => {
	const res = await serverApi.post(`/receives/multi`, data);
	return res;
};

export const getAllReceives = async (data) => {
	const res = await serverApi.get(
		`/receives?page=${data.queryKey[1]}&limit=${data.queryKey[2]}`
	);
	return res;
};
export const getReceiveById = async (data) => {
	const res = await serverApi.get(`/receives/${data.queryKey[1]}`);
	return res;
};
export const deleteReceives = async (id) => {
	const res = await serverApi.delete(`/receives`, { data: { ids: id } });
	return res;
};
//Pay API
export const addPay = async (data) => {
	const res = await serverApi.post(`/pay`, data);
	return res;
};
export const getAllPays = async () => {
	const res = await serverApi.get(`/pay`);
	return res;
};
export const deletePay = async (id) => {
	const res = await serverApi.delete(`/pay/${id}`);
	return res;
};

//Users API
export const addUser = async (data) => {
	const res = await serverApi.post("/users", data);
	return res;
};
export const getAllUsers = async () => {
	const res = await serverApi.get("/users");
	return res;
};
export const getUser = async (data) => {
	const res = await serverApi.get(`/users/${data.queryKey[1]}`);
	return res;
};
export const getUsername = async (data) => {
	const res = await serverApi.get(`/users/username/${data.queryKey[1]}`);
	return res;
};
export const editUser = async (data) => {
	const res = await serverApi.patch(`/users/${data.id}`, data);
	return res;
};
export const deleteUser = async (id) => {
	const res = await serverApi.delete(`/users/${id}`);
	return res;
};
//jobs API
export const getAllJobTitles = async () => {
	const res = await serverApi.get("/jobs/titles");
	return res;
};
export const getAllJobDegrees = async () => {
	const res = await serverApi.get("/jobs/degrees");
	return res;
};
export const getAllJobTypes = async () => {
	const res = await serverApi.get("/jobs/types");
	return res;
};
//Reports API
export const getReceiveReport = async (data) => {
	const res = await serverApi.post("/reports/receive", data);
	return res;
};
//Aalary Advance API
export const addSalaryAdvance = async (data) => {
	const res = await serverApi.post(`/salaryAdvance`, data);
	return res;
};
export const getSalaryAdvanceByFinancialNum = async (data) => {
	const res = await serverApi.get(`/salaryAdvance/fn/${data.queryKey[1]}`);
	return res;
};
export const getSalaryAdvancesForMultiBeneficiaries = async (data) => {
	const res = await serverApi.post(`/salaryAdvance/fn/`, data.queryKey[1]);
	return res;
};
export const getAllSalaryAdvances = async () => {
	const res = await serverApi.get(`/salaryAdvance`);
	return res;
};
export const deleteSalaryAdvance = async (id) => {
	const res = await serverApi.delete(`/salaryAdvance/${id}`);
	return res;
};
//whatsapp API
export const getQR = async () => {
	const res = await serverApi.get(`/whatsapp/getQR`);
	return res;
};
export const sendMessages = async (data) => {
	const res = await serverApi.post(`/whatsapp/sendMessages`, data);
	return res;
};

// export const getAllPays = async () => {
// 	const res = await serverApi.get(`/pay`);
// 	return res;
// };
// export const deletePay = async (id) => {
// 	const res = await serverApi.delete(`/pay/${id}`);
// 	return res;
// };
