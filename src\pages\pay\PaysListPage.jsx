import { useCallback, useContext, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { deletePay, getAllPays, getPaybyId } from "../../api/serverApi";
import { useMutation, useQuery, useQueryClient } from "react-query";
import {
	Modal,
	ModalContent,
	ModalHeader,
	ModalBody,
	ModalFooter,
	useDisclosure,
	Card,
	CardHeader,
	CardBody,
	Button,
	Table,
	TableHeader,
	TableColumn,
	TableBody,
	TableRow,
	TableCell,
	Tooltip,
	Pagination,
} from "@heroui/react";
import { Plus, Printer, Trash, X, Check } from "@mynaui/icons-react";
import TopBar from "../../components/TopBar/TopBar";
import { AuthContext } from "../../store/auth-context";
import { toast } from "react-toastify";
import tafqeet from "@/utils/Tafqeet";
const PaysListPage = () => {
	//hooks
	const navigate = useNavigate();
	const queryClient = useQueryClient();
	const authCtx = useContext(AuthContext);
	const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure();

	//states
	const [pays, setPays] = useState([]);
	const [payToFetch, setPayToFetch] = useState(null);
	const [modalMode, setModalMode] = useState("");
	const [modalPayload, setModalPayload] = useState(null);
	const [searchParams, setSearchParams] = useSearchParams();
	const page = parseInt(searchParams.get("page")) || 1;
	const rowsPerPage = parseInt(searchParams.get("rowsPerPage")) || 5;
	const [pages, setPages] = useState(1);
	const [total, setTotal] = useState("");
	//queries

	useQuery({
		queryKey: ["pays", page - 1, rowsPerPage],
		queryFn: getAllPays,
		select: (res) => {
			return res.data;
		},
		onSuccess: (data) => {
			setPays(data.pays);
			setPages(Math.ceil(data.total / rowsPerPage));
			setTotal(data.total);
		},
	});
	useQuery({
		queryKey: ["payData", payToFetch?.id],
		queryFn: getPaybyId,
		select: (res) => {
			return res.data.pay;
		},
		onSuccess: (data) => {
			setPayToFetch(null);
			const dataToPrint = `${data.date.split("T")[0].replace(/-/g, "/")}`;
			let reportTemplate = "";
			if (!data.is_list) {
				reportTemplate = "a5Pay";
			} else {
				reportTemplate = "a4Pay";
			}
			data.net_amount_text = tafqeet(data.net_amount);
			// data.dues.forEach((el) => {
			// 	el.amountAfterDeduction = +el.amount - +el.deduction;
			// 	el.tax = +el.tax;
			// });
			navigate("./print", {
				state: {
					data: {
						...data,
						date: dataToPrint,
					},
					reportTemplate,
				},
			});
		},
		enabled: !!payToFetch?.id,
	});
	const deleteMutation = useMutation({
		mutationFn: deletePay,
		onSuccess: () => {
			toast.success("تم الحذف بنجاح", {
				position: toast.POSITION.TOP_CENTER,
			});
			queryClient.invalidateQueries({ queryKey: ["pays"] });
			onClose();
		},
		onError: (err) => {
			toast.error(err.response.data.message, {
				position: toast.POSITION.TOP_CENTER,
			});
			onClose();
		},
	});
	//functions

	const handleDelete = (pay) => {
		setModalMode("delete");
		setModalPayload(pay);
		onOpen();
	};
	const onRowsPerPageChange = useCallback(
		(e) => {
			const newRowsPerPage = Number(e.target.value);
			setSearchParams({
				page: "1", // Reset to first page when changing rows per page
				rowsPerPage: newRowsPerPage.toString(),
			});
		},
		[setSearchParams]
	);
	const handlePageChange = (newPage) => {
		setSearchParams({
			page: newPage.toString(),
			rowsPerPage: rowsPerPage.toString(),
		});
	};
	return (
		<div className="w-screen h-screen overflow-auto ">
			{/* Modal */}
			<Modal isOpen={isOpen} onOpenChange={onOpenChange} size="lg">
				<ModalContent>
					{() => (
						<>
							<ModalHeader className="flex flex-col gap-1">
								{modalMode === "delete" && "حذف سند صرف مستحقات"}
							</ModalHeader>
							<ModalBody>
								{modalMode === "delete" && modalPayload && (
									<div>
										{`هل انت متاكد من حذف سند الصرف رقم ${modalPayload.id} بعنوان ${modalPayload.title} ؟`}
									</div>
								)}
							</ModalBody>
							<ModalFooter>
								{modalMode === "delete" && (
									<div className="flex gap-3">
										<Button
											variant="flat"
											onPress={onClose}
											color="danger"
											startContent={<X />}
										>
											الغاء
										</Button>
										<Button
											color="primary"
											onPress={() => {
												deleteMutation.mutate(modalPayload.id);
											}}
											startContent={<Check />}
										>
											تأكيد
										</Button>
									</div>
								)}
							</ModalFooter>
						</>
					)}
				</ModalContent>
			</Modal>
			<TopBar
				right={
					<>
						{/* <Button
							color="primary"
							startContent={<Plus />}
							onPress={() => {
								navigate("./add");
							}}
						>
							اضافة
						</Button> */}
					</>
				}
			/>
			<div className="w-full p-5 pb-16">
				{pays && pays.length > 0 && (
					<Card>
						<CardHeader className="bg-primary text-default-50 font-bold text-medium">
							كشف سندات الصرف
						</CardHeader>
						<CardBody>
							<div className="my-5">
								<Table
									aria-label="كشف سندات الصرف"
									bottomContent={
										<div className="py-2 px-2 flex justify-between items-center">
											<span className="text-default-400 text-small">
												الاجمالي {total} حركة
											</span>
											<Pagination
												showControls
												showShadow
												color="primary"
												page={page}
												total={pages}
												onChange={handlePageChange}
											/>
											<label className="flex items-center text-default-400 text-small">
												النتائج لكل صفحة:
												<select
													className="bg-transparent outline-none text-default-400 text-small"
													onChange={onRowsPerPageChange}
													value={rowsPerPage}
												>
													<option value="5">5</option>
													<option value="10">10</option>
													<option value="15">15</option>
												</select>
											</label>
										</div>
									}
								>
									<TableHeader>
										<TableColumn>م</TableColumn>
										<TableColumn>العنوان</TableColumn>
										<TableColumn>الاسم</TableColumn>
										<TableColumn>المبلغ</TableColumn>
										<TableColumn>خيارات</TableColumn>
									</TableHeader>
									<TableBody>
										{pays.map((pay) => (
											<TableRow key={pay.id}>
												<TableCell>{pay.id}</TableCell>
												<TableCell>{pay.title}</TableCell>
												<TableCell>{pay.name}</TableCell>
												<TableCell>{+pay.net_amount} ريال يمني</TableCell>
												<TableCell>
													<div className="flex gap-2">
														<Tooltip content="طباعة">
															<Button
																color="primary"
																startContent={<Printer />}
																size="sm"
																isIconOnly
																onPress={() => {
																	setPayToFetch({
																		id: pay.id,
																	});
																}}
															/>
														</Tooltip>
														<Tooltip content="حذف">
															<Button
																color="danger"
																startContent={<Trash />}
																size="sm"
																isIconOnly
																isDisabled={
																	!authCtx.permissions.deletePay ||
																	pay.state === "closed"
																}
																onPress={() => handleDelete(pay)}
															/>
														</Tooltip>
													</div>
												</TableCell>
											</TableRow>
										))}
									</TableBody>
								</Table>
							</div>
						</CardBody>
					</Card>
				)}
			</div>
		</div>
	);
};

export default PaysListPage;
